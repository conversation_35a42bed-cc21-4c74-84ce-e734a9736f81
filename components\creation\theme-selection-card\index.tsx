import React from 'react';
import { M3ECard } from '@/components/ui/m3e-card';
import { Text  } from '@/components/base';
import { View } from 'react-native';
import { StoryTheme } from '@/types/story';
import {
  BookOpen,
  Rocket,
  Wand,
  Landmark,
  Heart,
  Search,
  Building,
  Check,
} from 'lucide-react-native';
import { useColorScheme } from 'nativewind';

interface ThemeSelectionCardProps {
  theme: StoryTheme;
  isSelected: boolean;
  onSelect: () => void;
  className?: string;
}

export default function ThemeSelectionCard({
  theme: themeProp,
  isSelected,
  onSelect,
  className = '',
}: ThemeSelectionCardProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const renderIcon = () => {
    const iconColor = isSelected ? '#FFFFFF' : themeProp.color || '#6366F1'; // primary-500 as fallback
    const size = 24;

    switch (themeProp.icon) {
      case 'rocket':
        return <Rocket size={size} color={iconColor} />;
      case 'wand':
        return <Wand size={size} color={iconColor} />;
      case 'search':
        return <Search size={size} color={iconColor} />;
      case 'heart':
        return <Heart size={size} color={iconColor} />;
      case 'landmark':
        return <Landmark size={size} color={iconColor} />;
      case 'building':
        return <Building size={size} color={iconColor} />;
      default:
        return <BookOpen size={size} color={iconColor} />;
    }
  };

  // Dynamic styles based on selection state
  const dynamicStyle = {
    backgroundColor: isSelected
      ? themeProp.color
      : isDark
      ? '#1F2937'
      : '#FFFFFF', // bg-background-800 or white
    borderColor: themeProp.color || '#6366F1', // primary-500 as fallback
  };

  return (
    <M3ECard
      variant={isSelected ? 'elevated' : 'outlined'}
      className={`w-[48%] mb-4 self-start ${className}`}
      style={dynamicStyle}
      onPress={onSelect}
    >
      <View className="p-4 relative">
        <Text
          className={`font-bold text-base mb-1 ${
            isSelected
              ? 'text-white'
              : isDark
              ? 'text-typography-50'
              : 'text-typography-900'
          }`}
        >
          {themeProp.name}
        </Text>

        {renderIcon()}

        <Text
          className={`text-xs mt-1 min-h-[32px] ${
            isSelected
              ? 'text-white'
              : isDark
              ? 'text-typography-400'
              : 'text-typography-500'
          }`}
          numberOfLines={2}
        >
          {themeProp.description}
        </Text>

        {isSelected && (
          <View className="absolute top-2 right-2">
            <Check size={16} color="#FFFFFF" />
          </View>
        )}
      </View>
    </M3ECard>
  );
}
