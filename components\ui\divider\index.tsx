'use client';
import React from 'react';
import { Platform, View } from 'react-native';
import { cn } from '@/utils/nativewind-helpers';

interface DividerProps extends React.ComponentPropsWithoutRef<typeof View> {
  orientation?: 'horizontal' | 'vertical';
}

const Divider = React.forwardRef<React.ComponentRef<typeof View>, DividerProps>(
  function Divider({ className, orientation = 'horizontal', ...props }, ref) {
    return (
      <View
        ref={ref}
        {...props}
        aria-orientation={orientation}
        role={Platform.OS === 'web' ? 'separator' : undefined}
        className={cn(
          'bg-border',
          orientation === 'horizontal' ? 'h-px w-full' : 'w-px h-full',
          className
        )}
      />
    );
  }
);

Divider.displayName = 'Divider';

export { Divider };
