import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { useNotifications } from '../../hooks/use-notifications';
import NotificationItem from '../notification-item';
import EmptyNotificationsState from '../empty-notifications-state';
import { Notification, NotificationType } from '@/api/notifications/types';
import { Check } from 'lucide-react-native';

import { Text  } from '@/components/base';
import { Pressable } from '@/components/base';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

export default function NotificationsTab() {
  const { t } = useTranslation();
  const router = useRouter();
  const [selectedFilter, setSelectedFilter] = useState<NotificationType | null>(
    null
  );

  const {
    notifications,
    isLoading,
    hasMore,
    markAsRead,
    markAllAsRead,
    removeNotification,
    refreshNotifications,
    loadMoreNotifications,
    filterNotifications,
  } = useNotifications({ autoRefresh: true });

  // 处理通知点击
  const handleNotificationPress = (notification: Notification) => {
    // 根据通知类型导航到不同页面
    if (
      notification.type === 'like' ||
      notification.type === 'comment' ||
      notification.type === 'new_segment'
    ) {
      if (notification.story_id) {
        router.push(`/stories/${notification.story_id}`);
      }
    } else if (notification.type === 'follow') {
      if (notification.actor_id) {
        router.push(`/users/${notification.actor_id}`);
      }
    } else if (notification.type === 'new_story') {
      if (notification.story_id) {
        router.push(`/stories/${notification.story_id}`);
      }
    }
  };

  // 处理标记所有为已读
  const handleMarkAllAsRead = () => {
    Alert.alert(
      t('notifications.markAllAsReadTitle', '标记所有为已读'),
      t('notifications.markAllAsReadMessage', '确定要将所有通知标记为已读吗？'),
      [
        {
          text: t('common.cancel', '取消'),
          style: 'cancel',
        },
        {
          text: t('common.confirm', '确定'),
          onPress: () => markAllAsRead(),
        },
      ]
    );
  };

  // 处理筛选
  const handleFilterChange = (type: NotificationType | null) => {
    setSelectedFilter(type);
    filterNotifications(type, null);
  };

  // 渲染筛选按钮
  const renderFilterButton = (type: NotificationType | null, label: string) => {
    const isSelected = selectedFilter === type;
    return (
      <Pressable className={`px-4 py-2 rounded-full border ${
          isSelected
            ? 'bg-primary-500 border-primary-500'
            : 'bg-background-100 border-outline-300'
        } mr-2`}
        onPress={() => handleFilterChange(type)}
      >
        <Text
          className={`text-sm font-medium ${
            isSelected ? 'text-background-0' : 'text-typography-800'
          }`}
        >
          {label}
        </Text>
      </Pressable>
    );
  };

  // 渲染列表头部
  const renderHeader = () => (
    <View className="py-4 border-b border-outline-300">
      <View className="px-4 mb-4">
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {renderFilterButton(null, t('notifications.filters.all', '全部'))}
          {renderFilterButton('like', t('notifications.types.like', '点赞'))}
          {renderFilterButton(
            'comment',
            t('notifications.types.comment', '评论')
          )}
          {renderFilterButton(
            'follow',
            t('notifications.types.follow', '关注')
          )}
          {renderFilterButton(
            'mention',
            t('notifications.types.mention', '提及')
          )}
          {renderFilterButton(
            'new_story',
            t('notifications.types.newStory', '新故事')
          )}
          {renderFilterButton(
            'new_segment',
            t('notifications.types.newSegment', '新段落')
          )}
          {renderFilterButton(
            'system',
            t('notifications.types.system', '系统')
          )}
        </ScrollView>
      </View>

      {notifications.length > 0 && (
        <Pressable className="flex-row items-center justify-center py-2 mx-4 rounded-md border border-primary-500"
          onPress={handleMarkAllAsRead}
        >
          <Check size={16} color="#333333" />
          <Text className="text-sm font-medium text-primary-500 ml-1">
            {t('notifications.markAllAsRead', '全部已读')}
          </Text>
        </Pressable>
      )}
    </View>
  );

  // 渲染列表底部
  const renderFooter = () => {
    if (!isLoading) return null;
    return (
      <View className="p-4 items-center">
        <M3EProgressIndicator size="small" color="$primary500" />
      </View>
    );
  };

  // 渲染空状态
  const renderEmpty = () => {
    if (isLoading) return null;
    return <EmptyNotificationsState filterType={selectedFilter} />;
  };

  return (
    <View className="flex-1 bg-background-0">
      <FlatList
        data={notifications}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <NotificationItem
            notification={item}
            onPress={handleNotificationPress}
            onMarkAsRead={markAsRead}
            onDelete={removeNotification}
          />
        )}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        onRefresh={refreshNotifications}
        refreshing={isLoading && notifications.length === 0}
        onEndReached={loadMoreNotifications}
        onEndReachedThreshold={0.5}
        contentContainerStyle={
          notifications.length === 0 ? { flexGrow: 1 } : undefined
        }
      />
    </View>
  );
}
