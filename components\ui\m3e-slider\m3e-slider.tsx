import React, { useState, useCallback, useEffect } from 'react';
import { View, Pressable } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withSpring,
} from 'react-native-reanimated';
import { Text  } from '@/components/base';

/**
 * M3E Slider Component Props
 * Based on Material Design 3 Expressive specifications from Figma
 */
export interface M3ESliderProps {
  /** Current value of the slider */
  value?: number;
  /** Callback when value changes */
  onValueChange?: (value: number) => void;
  /** Minimum value */
  minimumValue?: number;
  /** Maximum value */
  maximumValue?: number;
  /** Step increment */
  step?: number;
  /** Whether the slider is disabled */
  disabled?: boolean;
  /** Orientation of the slider */
  orientation?: 'horizontal' | 'vertical';
  /** Size variant based on M3E specs (XSmall, Small, Medium, Large, XLarge) */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** Whether to show value indicator */
  showValueIndicator?: boolean;
  /** Whether to show track stops */
  showStops?: boolean;
  /** Number of stops to show */
  stopCount?: number;
  /** Custom className for container */
  className?: string;
  /** Custom className for track */
  trackClassName?: string;
  /** Custom className for active track */
  activeTrackClassName?: string;
  /** Custom className for thumb */
  thumbClassName?: string;
  /** Custom className for value indicator */
  valueIndicatorClassName?: string;
}

/**
 * Size configurations based on Material Design 3 Expressive Figma specs
 */
const getSizeConfig = (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl') => {
  const configs = {
    xs: {
      containerSize: 16,
      thumbSize: 16,
      trackHeight: 4,
      sliderLength: 200,
      thumbPressedSize: 20,
    },
    sm: {
      containerSize: 20,
      thumbSize: 20,
      trackHeight: 4,
      sliderLength: 240,
      thumbPressedSize: 24,
    },
    md: {
      containerSize: 24,
      thumbSize: 24,
      trackHeight: 4,
      sliderLength: 280,
      thumbPressedSize: 28,
    },
    lg: {
      containerSize: 32,
      thumbSize: 32,
      trackHeight: 4,
      sliderLength: 320,
      thumbPressedSize: 36,
    },
    xl: {
      containerSize: 40,
      thumbSize: 40,
      trackHeight: 4,
      sliderLength: 360,
      thumbPressedSize: 44,
    },
  };

  return configs[size];
};

/**
 * Get M3E state-based styles
 */
const getStateStyles = (state: 'enabled' | 'hovered' | 'pressed' | 'disabled') => {
  const styles = {
    enabled: {
      track: 'bg-outline-variant',
      activeTrack: 'bg-primary',
      thumb: 'bg-primary shadow-md',
      valueIndicator: 'bg-surface-container text-on-surface',
    },
    hovered: {
      track: 'bg-outline-variant',
      activeTrack: 'bg-primary',
      thumb: 'bg-primary shadow-lg',
      valueIndicator: 'bg-surface-container text-on-surface',
    },
    pressed: {
      track: 'bg-outline-variant',
      activeTrack: 'bg-primary',
      thumb: 'bg-primary shadow-xl',
      valueIndicator: 'bg-surface-container text-on-surface',
    },
    disabled: {
      track: 'bg-outline opacity-38',
      activeTrack: 'bg-on-surface opacity-38',
      thumb: 'bg-on-surface opacity-38',
      valueIndicator: 'bg-surface-container opacity-38 text-on-surface',
    },
  };

  return styles[state];
};

/**
 * M3E Slider Component
 * 
 * A Material Design 3 Expressive slider component with support for:
 * - Multiple sizes (XS, S, M, L, XL) based on Figma specs
 * - Horizontal and vertical orientations
 * - Multiple states (Enabled, Hovered, Pressed, Disabled)
 * - Value indicators and track stops
 * - Smooth animations and gestures
 * 
 * @param props - M3ESliderProps
 * @returns JSX.Element
 */
export const M3ESlider: React.FC<M3ESliderProps> = ({
  value = 50,
  onValueChange,
  minimumValue = 0,
  maximumValue = 100,
  step = 1,
  disabled = false,
  orientation = 'horizontal',
  size = 'md',
  showValueIndicator = false,
  showStops = false,
  stopCount = 5,
  className = '',
  trackClassName = '',
  activeTrackClassName = '',
  thumbClassName = '',
  valueIndicatorClassName = '',
}) => {
  const [currentValue, setCurrentValue] = useState(value);
  const [isPressed, setIsPressed] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  
  const isDragging = useSharedValue(false);
  const scale = useSharedValue(1);

  // Get size and style configurations
  const config = getSizeConfig(size);
  const currentState = disabled ? 'disabled' : isPressed ? 'pressed' : isHovered ? 'hovered' : 'enabled';
  const stateStyles = getStateStyles(currentState);

  // Sync external value changes
  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  // Calculate progress (0-1)
  const progress = (currentValue - minimumValue) / (maximumValue - minimumValue);

  // Update value with step and bounds checking
  const updateValue = useCallback((newValue: number) => {
    const clampedValue = Math.max(minimumValue, Math.min(maximumValue, newValue));
    const steppedValue = Math.round(clampedValue / step) * step;
    setCurrentValue(steppedValue);
    onValueChange?.(steppedValue);
  }, [minimumValue, maximumValue, step, onValueChange]);

  // Pan gesture handler
  const panGesture = Gesture.Pan()
    .onStart(() => {
      if (disabled) return;
      isDragging.value = true;
      scale.value = withSpring(1.2);
      setIsPressed(true);
    })
    .onUpdate((event) => {
      if (disabled) return;
      
      const gestureProgress = orientation === 'horizontal' 
        ? event.translationX / config.sliderLength
        : -event.translationY / config.sliderLength;
      
      const newValue = minimumValue + (gestureProgress * (maximumValue - minimumValue));
      runOnJS(updateValue)(newValue);
    })
    .onEnd(() => {
      if (disabled) return;
      isDragging.value = false;
      scale.value = withSpring(1);
      setIsPressed(false);
    });

  // Animated styles for thumb
  const thumbAnimatedStyle = useAnimatedStyle(() => {
    const thumbProgress = (currentValue - minimumValue) / (maximumValue - minimumValue);
    
    if (orientation === 'horizontal') {
      return {
        transform: [
          { translateX: thumbProgress * config.sliderLength - config.thumbSize / 2 },
          { scale: scale.value },
        ],
      };
    } else {
      return {
        transform: [
          { translateY: (1 - thumbProgress) * config.sliderLength - config.thumbSize / 2 },
          { scale: scale.value },
        ],
      };
    }
  });

  // Animated styles for active track
  const activeTrackAnimatedStyle = useAnimatedStyle(() => {
    if (orientation === 'horizontal') {
      return {
        width: progress * config.sliderLength,
      };
    } else {
      return {
        height: progress * config.sliderLength,
      };
    }
  });

  return (
    <View 
      className={`relative ${orientation === 'horizontal' ? 'w-full' : 'h-full'} ${className}`}
      style={{
        [orientation === 'horizontal' ? 'height' : 'width']: config.containerSize,
      }}
    >
      {/* Track Container */}
      <View 
        className={`relative ${orientation === 'horizontal' ? 'flex-row items-center' : 'flex-col items-center justify-end'}`}
        style={{
          [orientation === 'horizontal' ? 'width' : 'height']: config.sliderLength,
          [orientation === 'horizontal' ? 'height' : 'width']: config.containerSize,
        }}
      >
        {/* Inactive Track */}
        <View 
          className={`${stateStyles.track} rounded-full ${trackClassName}`}
          style={{
            [orientation === 'horizontal' ? 'width' : 'height']: config.sliderLength,
            [orientation === 'horizontal' ? 'height' : 'width']: config.trackHeight,
          }}
        />
        
        {/* Active Track */}
        <Animated.View 
          className={`${stateStyles.activeTrack} rounded-full absolute ${orientation === 'horizontal' ? 'left-0' : 'bottom-0'} ${activeTrackClassName}`}
          style={[
            activeTrackAnimatedStyle,
            {
              [orientation === 'horizontal' ? 'height' : 'width']: config.trackHeight,
            }
          ]}
        />

        {/* Track Stops */}
        {showStops && (
          <View className={`absolute ${orientation === 'horizontal' ? 'flex-row justify-between w-full' : 'flex-col justify-between h-full'}`}>
            {Array.from({ length: stopCount }, (_, index) => (
              <View
                key={index}
                className="bg-outline-variant rounded-full"
                style={{
                  width: 4,
                  height: 4,
                }}
              />
            ))}
          </View>
        )}
        
        {/* Thumb */}
        <GestureDetector gesture={panGesture}>
          <Animated.View 
            className={`absolute ${thumbClassName}`}
            style={thumbAnimatedStyle}
          >
            <Pressable onPressIn={() => !disabled && setIsPressed(true)}
              onPressOut={() => !disabled && setIsPressed(false)}
              onHoverIn={() => !disabled && setIsHovered(true)}
              onHoverOut={() => !disabled && setIsHovered(false)}
              disabled={disabled}
              className={`
                rounded-full shadow-md
                ${stateStyles.thumb}
              `}
              style={{
                width: isPressed ? config.thumbPressedSize : config.thumbSize,
                height: isPressed ? config.thumbPressedSize : config.thumbSize,
              }}
            />
          </Animated.View>
        </GestureDetector>
      </View>
      
      {/* Value Indicator */}
      {showValueIndicator && (
        <View 
          className={`absolute ${stateStyles.valueIndicator} rounded-md px-2 py-1 shadow-sm ${valueIndicatorClassName}`}
          style={{
            [orientation === 'horizontal' ? 'top' : 'left']: -40,
            [orientation === 'horizontal' ? 'left' : 'top']: progress * config.sliderLength - 20,
          }}
        >
          <Text className="text-xs font-medium">
            {Math.round(currentValue)}
          </Text>
        </View>
      )}
    </View>
  );
};

export default M3ESlider;
