import React, { useState } from 'react';
import { Text } from '@/components/base';
import { View, Text, TouchableOpacity, Modal } from 'react-native';

/**
 * M3E Tooltip 组件的属性接口
 */
export interface M3ETooltipProps {
  /** 触发元素 */
  children: React.ReactNode;
  /** 工具提示内容 */
  content: string;
  /** 工具提示类型 */
  type?: 'plain' | 'rich';
  /** 是否为多行 */
  multiline?: boolean;
  /** 标题（仅用于 rich 类型） */
  title?: string;
  /** 操作按钮（仅用于 rich 类型） */
  actions?: Array<{
    label: string;
    onPress: () => void;
    primary?: boolean;
  }>;
  /** 工具提示位置 */
  placement?: 'top' | 'bottom' | 'left' | 'right';
  /** 是否显示 */
  visible?: boolean;
  /** 显示状态变化回调 */
  onVisibleChange?: (visible: boolean) => void;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 获取 Tooltip 的样式类
 */
const getTooltipStyles = (type: string, multiline: boolean) => {
  const baseTooltip = 'absolute z-50 shadow-lg';
  
  if (type === 'plain') {
    return {
      tooltip: `${baseTooltip} bg-gray-800 dark:bg-gray-200 rounded px-2 py-1 ${
        multiline ? 'max-w-48' : ''
      }`,
      content: `text-xs text-white dark:text-gray-900 ${
        multiline ? 'leading-4' : ''
      }`,
      arrow: 'absolute w-2 h-2 bg-gray-800 dark:bg-gray-200 transform rotate-45'
    };
  } else {
    // Rich tooltip
    return {
      tooltip: `${baseTooltip} bg-purple-50 dark:bg-purple-900/20 rounded-xl p-0 max-w-80`,
      content: 'p-4 pb-2',
      title: 'text-sm font-medium text-gray-900 dark:text-white mb-1',
      description: 'text-sm text-gray-600 dark:text-gray-400',
      actions: 'flex flex-row justify-end gap-2 px-1 pb-2',
      actionButton: 'px-4 py-2 rounded-full',
      primaryAction: 'text-purple-600 dark:text-purple-400 font-medium',
      secondaryAction: 'text-gray-600 dark:text-gray-400',
      arrow: 'absolute w-2 h-2 bg-purple-50 dark:bg-purple-900/20 transform rotate-45'
    };
  }
};

/**
 * 获取箭头位置样式
 */
const getArrowPosition = (placement: string) => {
  switch (placement) {
    case 'top':
      return 'bottom-[-4px] left-1/2 -translate-x-1/2';
    case 'bottom':
      return 'top-[-4px] left-1/2 -translate-x-1/2';
    case 'left':
      return 'right-[-4px] top-1/2 -translate-y-1/2';
    case 'right':
      return 'left-[-4px] top-1/2 -translate-y-1/2';
    default:
      return 'bottom-[-4px] left-1/2 -translate-x-1/2';
  }
};

/**
 * 获取工具提示位置样式
 */
const getTooltipPosition = (placement: string) => {
  switch (placement) {
    case 'top':
      return 'bottom-full mb-2 left-1/2 -translate-x-1/2';
    case 'bottom':
      return 'top-full mt-2 left-1/2 -translate-x-1/2';
    case 'left':
      return 'right-full mr-2 top-1/2 -translate-y-1/2';
    case 'right':
      return 'left-full ml-2 top-1/2 -translate-y-1/2';
    default:
      return 'bottom-full mb-2 left-1/2 -translate-x-1/2';
  }
};

/**
 * M3E Tooltip 组件
 * 
 * 基于 Material Design 3 规范的工具提示组件，提供上下文支持信息。
 * 
 * @example
 * ```tsx
 * // 简单工具提示
 * <M3ETooltip content="This is a tooltip">
 *   <TouchableOpacity>
 *     <Text>Hover me</Text>
 *   </TouchableOpacity>
 * </M3ETooltip>
 * 
 * // 多行工具提示
 * <M3ETooltip 
 *   content="This is a longer tooltip that spans multiple lines"
 *   multiline={true}
 *   placement="top"
 * >
 *   <TouchableOpacity>
 *     <Text>Hover me</Text>
 *   </TouchableOpacity>
 * </M3ETooltip>
 * 
 * // 富文本工具提示
 * <M3ETooltip
 *   type="rich"
 *   title="Rich Tooltip"
 *   content="This is a rich tooltip with actions"
 *   actions={[
 *     { label: 'Cancel', onPress: () => {} },
 *     { label: 'OK', onPress: () => {}, primary: true }
 *   ]}
 * >
 *   <TouchableOpacity>
 *     <Text>Show rich tooltip</Text>
 *   </TouchableOpacity>
 * </M3ETooltip>
 * ```
 */
export const M3ETooltip: React.FC<M3ETooltipProps> = ({
  children,
  content,
  type = 'plain',
  multiline = false,
  title,
  actions = [],
  placement = 'top',
  visible: controlledVisible,
  onVisibleChange,
  className = '',
}) => {
  const [internalVisible, setInternalVisible] = useState(false);
  
  const isVisible = controlledVisible !== undefined ? controlledVisible : internalVisible;
  const styles = getTooltipStyles(type, multiline);
  
  const handlePress = () => {
    const newVisible = !isVisible;
    if (controlledVisible === undefined) {
      setInternalVisible(newVisible);
    }
    onVisibleChange?.(newVisible);
  };

  const handleClose = () => {
    if (controlledVisible === undefined) {
      setInternalVisible(false);
    }
    onVisibleChange?.(false);
  };

  const renderTooltipContent = () => {
    if (type === 'plain') {
      return (
        <Text className={styles.content} numberOfLines={multiline ? undefined : 1}>
          {content}
        </Text>
      );
    } else {
      return (
        <>
          <View className={styles.content}>
            {title && (
              <Text className={styles.title}>{title}</Text>
            )}
            <Text className={styles.description}>{content}</Text>
          </View>
          
          {actions.length > 0 && (
            <View className={styles.actions}>
              {actions.map((action, index) => (
                <TouchableOpacity
                  key={index}
                  className={`${styles.actionButton} ${
                    action.primary ? styles.primaryAction : styles.secondaryAction
                  }`}
                  onPress={() => {
                    action.onPress();
                    handleClose();
                  }}
                >
                  <Text className={action.primary ? styles.primaryAction : styles.secondaryAction}>
                    {action.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </>
      );
    }
  };

  const tooltipPositionClasses = getTooltipPosition(placement);
  const arrowPositionClasses = getArrowPosition(placement);

  return (
    <View className="relative">
      <TouchableOpacity onPress={handlePress} activeOpacity={0.7}>
        {children}
      </TouchableOpacity>
      
      {isVisible && (
        <View className={`${styles.tooltip} ${tooltipPositionClasses} ${className}`}>
          {renderTooltipContent()}
          
          {/* 箭头 */}
          <View className={`${styles.arrow} ${arrowPositionClasses}`} />
        </View>
      )}
    </View>
  );
};

/**
 * M3E Tooltip 变体组件
 */

/**
 * 简单工具提示
 */
export const M3ETooltipPlain: React.FC<M3ETooltipProps> = (props) => (
  <M3ETooltip {...props} type="plain" />
);

/**
 * 富文本工具提示
 */
export const M3ETooltipRich: React.FC<M3ETooltipProps> = (props) => (
  <M3ETooltip {...props} type="rich" />
);

/**
 * 多行工具提示
 */
export const M3ETooltipMultiline: React.FC<M3ETooltipProps> = (props) => (
  <M3ETooltip {...props} multiline={true} />
);

export default M3ETooltip;
