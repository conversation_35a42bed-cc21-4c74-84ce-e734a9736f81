import React from 'react';
import { Image , View } from 'react-native';
import { useTranslation } from 'react-i18next';
import {
  MessageCircle,
  Bookmark,
  MoreHorizontal,
  GitBranch,
  ThumbsUp,
  ThumbsDown,
} from 'lucide-react-native';
import { StorySegment } from '@/api/stories/types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useI18n } from '@/hooks/use-i18n';
import { getChildrenCount } from '@/utils/story-helpers';

// Import gluestack-ui components

import { Text  } from '@/components/base';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';


interface StoryNodeCardProps {
  segment: StorySegment;
  isOriginStory?: boolean;
  onBranchPress: () => void;
  hasBranches?: boolean;
}

export default function StoryNodeCard({
  segment,
  isOriginStory = false,
  onBranchPress,
  hasBranches: propHasBranches,
}: StoryNodeCardProps) {
  const { t } = useTranslation();
  const { currentLanguage } = useI18n();

  // Format the relative time (e.g., "2h", "1d")
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const locale = currentLanguage === 'zh' ? zhCN : enUS;
      return formatDistanceToNow(date, { locale, addSuffix: false });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // 使用工具函数获取子分支数量
  const childrenCount = getChildrenCount(segment.children_count);

  // 如果传入了 hasBranches 属性，则使用该属性，否则根据子分支数量决定
  const hasBranches =
    propHasBranches !== undefined ? propHasBranches : childrenCount > 0;

  // 用于显示的子分支数量，使用实际的子分支数量
  const displayChildrenCount = childrenCount;

  console.log(
    'StoryNodeCard - segment:',
    segment.id,
    'parent_segment_id:',
    segment.parent_segment_id,
    'children_count:',
    displayChildrenCount,
    'hasBranches:',
    hasBranches,
    'content:',
    segment.content.substring(0, 30)
  );

  return (
    <View className={`bg-surface-50 dark:bg-surface-900 rounded-md border mb-4 p-4 shadow-sm ${
        isOriginStory
          ? 'border-warning-500 border-2'
          : 'border-outline-200 dark:border-outline-700'
      }`}
    >
      {/* Creator Info Row */}
      <View className="flex flex-row justify-between items-center mb-2 h-6">
        <View className="flex flex-row items-center flex-1">
          <Image
            source={
              segment.profiles?.avatar_url
                ? { uri: segment.profiles.avatar_url }
                : require('@/assets/images/default-avatar.png')
            }
            className="w-5 h-5 rounded-full mr-1"
          />
          <Text
            className="text-sm font-medium text-typography-900 dark:text-typography-100 mr-1"
            numberOfLines={1}
          >
            {segment.profiles?.username || t('common.unknownUser', '未知用户')}
          </Text>
          <Text
            className="text-xs text-typography-500 dark:text-typography-400 mr-1"
            numberOfLines={1}
          >
            @{segment.profiles?.username || 'username'}
          </Text>
          <Text className="text-xs text-typography-500 dark:text-typography-400">
            {formatRelativeTime(segment.created_at)}
          </Text>
        </View>

        <Button action="secondary" variant="link" size="sm" className="p-1">
          <ButtonIcon as={MoreHorizontal} size="sm" />
        </Button>
      </View>

      {/* Story Content */}
      <Text className="text-base text-typography-900 dark:text-typography-100 leading-6 mb-4">
        {segment.content}
      </Text>

      {/* Interaction Row */}
      <View className="flex flex-row justify-between items-center">
        <View className="flex flex-row items-center">
          <Button
            action="secondary"
            variant="link"
            size="sm"
            className="flex-row items-center mr-3"
          >
            <ButtonIcon as={MessageCircle} size="sm" />
            <ButtonText className="ml-1 text-xs">0</ButtonText>
          </Button>

          <Button
            action="secondary"
            variant="link"
            size="sm"
            className="flex-row items-center mr-3"
          >
            <ButtonIcon as={ThumbsUp} size="sm" />
            <ButtonText className="ml-1 text-xs">0</ButtonText>
          </Button>

          <Button
            action="secondary"
            variant="link"
            size="sm"
            className="flex-row items-center mr-3"
          >
            <ButtonIcon as={ThumbsDown} size="sm" />
            <ButtonText className="ml-1 text-xs">0</ButtonText>
          </Button>

          <Button
            action="secondary"
            variant="link"
            size="sm"
            className="flex-row items-center mr-3"
          >
            <ButtonIcon as={Bookmark} size="sm" />
            <ButtonText className="ml-1 text-xs">0</ButtonText>
          </Button>
        </View>

        {hasBranches ? (
          <Button
            action={isOriginStory ? 'secondary' : 'primary'}
            variant="solid"
            size="sm"
            onPress={onBranchPress}
            accessibilityLabel={t('storyDetail.viewBranches', 'View branches')}
            className="flex-row items-center px-2 py-1 rounded-full"
          >
            <ButtonIcon as={GitBranch} size="sm" />
            <ButtonText className="ml-1 text-xs">
              {displayChildrenCount}
            </ButtonText>
          </Button>
        ) : (
          <View className="w-10" />
        )}
      </View>
    </View>
  );
}
