import React, { useState, useEffect } from 'react';
import { FlatList, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Stack, useRouter } from 'expo-router';
import { Search, X, Check } from 'lucide-react-native';
import { mockUsers } from '@/utils/mock-data/users';
import { useConversations } from '../../hooks/use-conversations';

import { Text, Pressable } from '@/components/base';
import { Image } from 'expo-image';
import { M3ETextField } from '@/components/ui/m3e-text-field';
import { Button, ButtonText } from '@/components/ui/m3e-button';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

export default function NewConversationScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredUsers, setFilteredUsers] = useState(mockUsers);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const { startConversation } = useConversations();

  // 过滤用户
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredUsers(mockUsers);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = mockUsers.filter(
        (user) =>
          user.displayName.toLowerCase().includes(query) ||
          user.username.toLowerCase().includes(query)
      );
      setFilteredUsers(filtered);
    }
  }, [searchQuery]);

  // 处理用户选择
  const handleUserSelect = (userId: string) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter((id) => id !== userId));
    } else {
      setSelectedUsers([...selectedUsers, userId]);
    }
  };

  // 处理创建对话
  const handleCreateConversation = async () => {
    if (selectedUsers.length === 0 || isCreating) {
      return;
    }

    setIsCreating(true);
    try {
      const conversation = await startConversation({
        participant_ids: selectedUsers,
      });

      if (conversation) {
        router.replace(`/messages/${conversation.id}`);
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
    } finally {
      setIsCreating(false);
    }
  };

  // 渲染用户项
  const renderUserItem = ({ item }: { item: (typeof mockUsers)[0] }) => {
    const isSelected = selectedUsers.includes(item.id);
    return (
      <Pressable
        className={`flex-row items-center justify-between py-4 border-b border-outline-300 ${
          isSelected ? 'bg-background-100' : ''
        }`}
        onPress={() => handleUserSelect(item.id)}
      >
        <View className="flex-row items-center">
          {item.avatar ? (
            <Image
              source={{ uri: item.avatar }}
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                marginRight: 16,
              }}
              contentFit="cover"
            />
          ) : (
            <View className="w-10 h-10 rounded-full bg-background-300 mr-4" />
          )}
          <View className="flex-1">
            <Text className="text-base font-medium text-typography-900">
              {item.displayName}
            </Text>
            <Text className="text-sm text-typography-600">
              @{item.username}
            </Text>
          </View>
        </View>

        <View
          className={`w-6 h-6 rounded-full border items-center justify-center ${
            isSelected
              ? 'bg-primary-500 border-primary-500'
              : 'border-outline-400'
          }`}
        >
          {isSelected && <Check size={16} color="white" />}
        </View>
      </Pressable>
    );
  };

  const isDisabled = selectedUsers.length === 0 || isCreating;

  return (
    <View className="flex-1 bg-background-0">
      <Stack.Screen
        options={{
          title: t('messages.newConversation', '新建对话'),
          headerShown: true,
          headerRight: () => (
            <Button
              className={`px-4 py-2 rounded-md ${
                isDisabled ? 'bg-background-400' : 'bg-primary-500'
              }`}
              onPress={handleCreateConversation}
              isDisabled={isDisabled}
            >
              {isCreating ? (
                <M3EProgressIndicator size="small" color="white" />
              ) : (
                <ButtonText className="text-sm font-medium text-white">
                  {t('messages.create', '创建')}
                </ButtonText>
              )}
            </Button>
          ),
        }}
      />

      <View className="flex-row items-center bg-background-100 rounded-md mx-4 my-4 px-4">
        <Search size={16} color="#666" style={{ marginRight: 8 }} />
        <M3ETextField
          className="flex-1 h-12 text-base text-typography-900"
          placeholder={t('messages.searchUsers', '搜索用户...')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoFocus
        />
        {searchQuery.length > 0 && (
          <Pressable className="p-2" onPress={() => setSearchQuery('')}>
            <X size={16} color="#666" />
          </Pressable>
        )}
      </View>

      {selectedUsers.length > 0 && (
        <View className="mx-4 mb-4">
          <Text className="text-sm font-medium text-typography-600 mb-2">
            {t('messages.selectedUsers', '已选择的用户')}
            {' ('}
            {selectedUsers.length}
            {')'}
          </Text>
          <FlatList
            data={mockUsers.filter((user) => selectedUsers.includes(user.id))}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View className="flex-row items-center bg-primary-500 rounded-full px-4 py-2 mr-2">
                <Text className="text-sm font-medium text-white mr-2">
                  {item.displayName}
                </Text>
                <Pressable
                  className="w-4 h-4 rounded-full bg-black/20 justify-center items-center"
                  onPress={() => handleUserSelect(item.id)}
                >
                  <X size={12} color="white" />
                </Pressable>
              </View>
            )}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingVertical: 8 }}
          />
        </View>
      )}

      <FlatList
        data={filteredUsers}
        keyExtractor={(item) => item.id}
        renderItem={renderUserItem}
        contentContainerStyle={{ paddingHorizontal: 16 }}
      />
    </View>
  );
}
