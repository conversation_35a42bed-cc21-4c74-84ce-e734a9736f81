import React, { useState } from 'react';
import { Text } from '@/components/base';
import { View, Text, TouchableOpacity } from 'react-native';

// Radio Button 的属性接口
export interface M3ERadioButtonProps {
  /** 是否选中 */
  selected?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 标签文本 */
  label?: string;
  /** 值 */
  value?: string | number;
  /** 选择变化事件 */
  onValueChange?: (value: string | number) => void;
  /** 自定义样式类名 */
  className?: string;
}

// Radio Group 的属性接口
export interface M3ERadioGroupProps {
  /** 当前选中的值 */
  value?: string | number;
  /** 选项列表 */
  options: Array<{
    label: string;
    value: string | number;
    disabled?: boolean;
  }>;
  /** 是否禁用整个组 */
  disabled?: boolean;
  /** 布局方向 */
  direction?: 'row' | 'column';
  /** 值变化事件 */
  onValueChange?: (value: string | number) => void;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * M3E Radio Button 组件
 *
 * 基于 Material Design 3 规范的单选按钮组件。
 *
 * @example
 * ```tsx
 * <M3ERadioButton
 *   selected={selectedValue === 'option1'}
 *   label="Option 1"
 *   value="option1"
 *   onValueChange={(value) => setSelectedValue(value)}
 * />
 * ```
 */
export const M3ERadioButton: React.FC<M3ERadioButtonProps> = ({
  selected = false,
  disabled = false,
  label,
  value,
  onValueChange,
  className = '',
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const handlePress = () => {
    if (!disabled && onValueChange && value !== undefined) {
      onValueChange(value);
    }
  };

  const getContainerClasses = () => {
    let classes = 'flex-row items-center';
    
    if (disabled) {
      classes += ' opacity-40';
    }
    
    return `${classes} ${className}`;
  };

  const getRadioClasses = () => {
    let classes = 'w-12 h-12 rounded-full items-center justify-center';
    
    // State layer background
    if (disabled) {
      classes += ' bg-transparent';
    } else if (isPressed) {
      if (selected) {
        classes += ' bg-purple-100 dark:bg-purple-900';
      } else {
        classes += ' bg-gray-100 dark:bg-gray-800';
      }
    } else if (isFocused) {
      classes += ' bg-gray-100 dark:bg-gray-800';
    } else {
      classes += ' bg-transparent';
    }
    
    return classes;
  };

  const getIconClasses = () => {
    let classes = 'w-6 h-6 rounded-full border-2 items-center justify-center';
    
    if (disabled) {
      classes += ' border-gray-400 dark:border-gray-600';
    } else if (selected) {
      classes += ' border-purple-600 dark:border-purple-400 bg-transparent';
    } else {
      classes += ' border-gray-600 dark:border-gray-400 bg-transparent';
    }
    
    return classes;
  };

  const getInnerDotClasses = () => {
    let classes = 'w-2.5 h-2.5 rounded-full';
    
    if (selected) {
      if (disabled) {
        classes += ' bg-gray-400 dark:bg-gray-600';
      } else {
        classes += ' bg-purple-600 dark:bg-purple-400';
      }
    } else {
      classes += ' bg-transparent';
    }
    
    return classes;
  };

  const getLabelClasses = () => {
    let classes = 'text-base font-normal ml-3';
    
    if (disabled) {
      classes += ' text-gray-400 dark:text-gray-600';
    } else {
      classes += ' text-gray-900 dark:text-white';
    }
    
    return classes;
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      onPressIn={() => setIsPressed(true)}
      onPressOut={() => setIsPressed(false)}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
      activeOpacity={disabled ? 1 : 0.7}
      className={getContainerClasses()}
      disabled={disabled}
    >
      <View className={getRadioClasses()}>
        <View className={getIconClasses()}>
          <View className={getInnerDotClasses()} />
        </View>
      </View>
      
      {label && (
        <Text className={getLabelClasses()}>
          {label}
        </Text>
      )}
    </TouchableOpacity>
  );
};

/**
 * M3E Radio Group 组件
 *
 * 管理一组单选按钮的组件。
 *
 * @example
 * ```tsx
 * const options = [
 *   { label: 'Option 1', value: 'option1' },
 *   { label: 'Option 2', value: 'option2' },
 *   { label: 'Option 3', value: 'option3', disabled: true },
 * ];
 * 
 * <M3ERadioGroup
 *   value={selectedValue}
 *   options={options}
 *   direction="column"
 *   onValueChange={setSelectedValue}
 * />
 * ```
 */
export const M3ERadioGroup: React.FC<M3ERadioGroupProps> = ({
  value,
  options,
  disabled = false,
  direction = 'column',
  onValueChange,
  className = '',
}) => {
  const getContainerClasses = () => {
    let classes = direction === 'row' ? 'flex-row' : 'flex-col';
    
    if (direction === 'row') {
      classes += ' gap-6';
    } else {
      classes += ' gap-4';
    }
    
    return `${classes} ${className}`;
  };

  return (
    <View className={getContainerClasses()}>
      {options.map((option, index) => (
        <M3ERadioButton
          key={`${option.value}-${index}`}
          selected={value === option.value}
          disabled={disabled || option.disabled}
          label={option.label}
          value={option.value}
          onValueChange={onValueChange}
        />
      ))}
    </View>
  );
};

export default M3ERadioButton;
