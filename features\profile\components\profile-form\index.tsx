import React from 'react';
import { TextInput , View } from 'react-native';
import { useTranslation } from 'react-i18next';

// Gluestack UI components

import { Text  } from '@/components/base';
import { Button, ButtonText } from '@/components/ui/m3e-button';
import { VStack } from '@/components/ui/vstack/index';
import { Center } from '@/components/ui/center/index';

// Components
import AvatarPicker from '../avatar-picker';

interface ProfileFormProps {
  username: string;
  setUsername: (value: string) => void;
  fullName: string;
  setFullName: (value: string) => void;
  bio: string;
  setBio: (value: string) => void;
  avatarUrl: string | null;
  newAvatarUri: string | null;
  uploadingAvatar: boolean;
  saving: boolean;
  onAvatarSelected: (uri: string) => void;
  onSave: () => void;
}

export default function ProfileForm({
  username,
  setUsername,
  fullName,
  setFullName,
  bio,
  setBio,
  avatarUrl,
  newAvatarUri,
  uploadingAvatar,
  saving,
  onAvatarSelected,
  onSave,
}: ProfileFormProps) {
  const { t } = useTranslation();

  return (
    <>
      <View className="flex justify-center items-center mb-8">
        <AvatarPicker
          avatarUrl={newAvatarUri || avatarUrl}
          onImageSelected={onAvatarSelected}
          loading={uploadingAvatar}
          size={120}
        />
      </View>

      <View className="flex flex-col space-y-4 mb-6">
        <View>
          <Text className="text-sm font-medium mb-1 text-typography-900 dark:text-typography-100">
            {t('usernameLabel', 'Username')}
          </Text>
          <View className="bg-background-50 dark:bg-background-800 border border-outline-200 dark:border-outline-700 rounded-md px-3 py-2">
            <TextInput
              value={username}
              onChangeText={setUsername}
              placeholder={t('usernamePlaceholder', 'Enter your username')}
              autoCapitalize="none"
              placeholderTextColor="#666"
              style={{ color: 'var(--typography-900, #000)', fontSize: 16 }}
              className="text-typography-900 dark:text-typography-100"
            />
          </View>
        </View>

        <View>
          <Text className="text-sm font-medium mb-1 text-typography-900 dark:text-typography-100">
            {t('fullNameLabel', 'Full Name')}
          </Text>
          <View className="bg-background-50 dark:bg-background-800 border border-outline-200 dark:border-outline-700 rounded-md px-3 py-2">
            <TextInput
              value={fullName}
              onChangeText={setFullName}
              placeholder={t('fullNamePlaceholder', 'Enter your full name')}
              placeholderTextColor="#666"
              style={{ fontSize: 16 }}
              className="text-typography-900 dark:text-typography-100"
            />
          </View>
        </View>

        <View>
          <Text className="text-sm font-medium mb-1 text-typography-900 dark:text-typography-100">
            {t('bioLabel', 'Bio')}
          </Text>
          <View className="bg-background-50 dark:bg-background-800 border border-outline-200 dark:border-outline-700 rounded-md px-3 py-2 h-24">
            <TextInput
              value={bio}
              onChangeText={setBio}
              placeholder={t('bioPlaceholder', 'Tell us about yourself')}
              multiline
              numberOfLines={4}
              placeholderTextColor="#666"
              style={{
                fontSize: 16,
                height: '100%',
                textAlignVertical: 'top',
              }}
              className="text-typography-900 dark:text-typography-100"
            />
          </View>
        </View>
      </View>

      <Button
        size="lg"
        variant="solid"
        action="primary"
        onPress={onSave}
        isDisabled={saving || uploadingAvatar}
        className="mt-4 mb-8 py-3 rounded-md bg-primary-500 dark:bg-primary-600"
      >
        <ButtonText className="font-medium text-typography-950 dark:text-typography-50">
          {saving ? t('saving', 'Saving...') : t('saveChanges', 'Save Changes')}
        </ButtonText>
      </Button>
    </>
  );
}
