import React from 'react';
import { ViewStyle, StyleProp } from 'react-native';

import { Text  } from '@/components/base';
import { Pressable } from '@/components/base';
import { useColorScheme } from 'nativewind';

// Define the structure for each option
export interface Option<T> {
  value: T;
  label: string;
}

interface OptionsGroupProps<T> {
  options: Option<T>[];
  selectedValue: T;
  onSelect: (value: T) => void;
  className?: string; // Optional className prop for the container
  style?: StyleProp<ViewStyle>; // Optional style prop for backward compatibility
}

export function OptionsGroup<T>({
  options,
  selectedValue,
  onSelect,
  className = '',
  style,
}: OptionsGroupProps<T>) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View className={`flex-row justify-around bg-background-100 dark:bg-background-800 rounded-lg p-2 border border-outline-200 dark:border-outline-700 self-stretch ${className}`}
      style={style}
    >
      {options.map((option) => (
        <Pressable key={option.label} // Use label as key assuming labels are unique for a group
          className={`flex-1 py-2 px-4 rounded items-center ${
            selectedValue === option.value ? 'bg-primary-500' : ''
          }`}
          onPress={() => onSelect(option.value)}
        >
          <Text
            className={`${
              selectedValue === option.value
                ? isDark
                  ? 'text-black'
                  : 'text-white'
                : 'text-typography-900 dark:text-typography-50'
            } font-medium`}
          >
            {option.label}
          </Text>
        </Pressable>
      ))}
    </View>
  );
}
