/**
 * 基础按钮组件
 * 替代 Gluestack UI 的 Button 组件
 */

import React from 'react';
import { Pressable, PressableProps, Text, View } from 'react-native';
import { cn, createVariants } from '@/utils/nativewind-helpers';

// Button 组件
const buttonVariants = createVariants({
  base: 'flex-row items-center justify-center rounded-lg transition-colors',
  variants: {
    variant: {
      solid: 'bg-primary-500 active:bg-primary-600',
      outline:
        'border border-primary-500 bg-transparent active:bg-primary-50 dark:active:bg-primary-900',
      ghost: 'bg-transparent active:bg-primary-50 dark:active:bg-primary-900',
      link: 'bg-transparent active:bg-transparent underline',
    },
    size: {
      xs: 'h-6 px-2',
      sm: 'h-8 px-3',
      md: 'h-10 px-4',
      lg: 'h-12 px-6',
      xl: 'h-14 px-8',
    },
    action: {
      primary: '',
      secondary: 'bg-secondary-500 active:bg-secondary-600',
      positive: 'bg-success-500 active:bg-success-600',
      negative: 'bg-error-500 active:bg-error-600',
    },
    isDisabled: {
      true: 'opacity-50',
    },
  },
  defaultVariants: {
    variant: 'solid',
    size: 'md',
    action: 'primary',
  },
});

const buttonTextVariants = createVariants({
  base: 'font-medium text-center',
  variants: {
    variant: {
      solid: 'text-white',
      outline: 'text-primary-500',
      ghost: 'text-primary-500',
      link: 'text-primary-500',
    },
    size: {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl',
    },
    action: {
      primary: '',
      secondary: 'text-white',
      positive: 'text-white',
      negative: 'text-white',
    },
  },
  defaultVariants: {
    variant: 'solid',
    size: 'md',
    action: 'primary',
  },
});

export interface ButtonProps extends PressableProps {
  className?: string;
  variant?: 'solid' | 'outline' | 'ghost' | 'link';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  action?: 'primary' | 'secondary' | 'positive' | 'negative';
  isDisabled?: boolean;
  children?: React.ReactNode;
}

export const Button = React.forwardRef<
  React.ComponentRef<typeof Pressable>,
  ButtonProps
>(function Button(
  {
    className,
    variant,
    size,
    action,
    isDisabled,
    disabled,
    children,
    ...props
  },
  ref
) {
  const isButtonDisabled = isDisabled || disabled;

  return (
    <Pressable
      ref={ref}
      {...props}
      disabled={isButtonDisabled}
      className={buttonVariants({
        variant,
        size,
        action,
        isDisabled: isButtonDisabled,
        class: className,
      })}
    >
      {children}
    </Pressable>
  );
});

Button.displayName = 'Button';

export interface ButtonTextProps {
  className?: string;
  variant?: 'solid' | 'outline' | 'ghost' | 'link';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  action?: 'primary' | 'secondary' | 'positive' | 'negative';
  children?: React.ReactNode;
}

export const ButtonText: React.FC<ButtonTextProps> = ({
  className,
  variant,
  size,
  action,
  children,
}) => {
  return (
    <Text
      className={buttonTextVariants({
        variant,
        size,
        action,
        class: className,
      })}
    >
      {children}
    </Text>
  );
};

ButtonText.displayName = 'ButtonText';

export interface ButtonIconProps {
  className?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  as?: React.ComponentType<any>;
  children?: React.ReactNode;
}

export const ButtonIcon: React.FC<ButtonIconProps> = ({
  className,
  size,
  as: IconComponent,
  children,
  ...props
}) => {
  const iconSizes = {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 20,
    xl: 24,
  };

  if (IconComponent) {
    return (
      <IconComponent
        size={iconSizes[size || 'md']}
        className={cn('text-current', className)}
        {...props}
      />
    );
  }

  return (
    <View className={cn('items-center justify-center', className)}>
      {children}
    </View>
  );
};

ButtonIcon.displayName = 'ButtonIcon';
