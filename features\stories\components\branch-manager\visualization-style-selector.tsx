import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

// Import gluestack-ui components

import { Text  } from '@/components/base';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';



interface VisualizationStyleSelectorProps {
  visualizationStyle: 'tree' | 'flow' | 'network' | 'timeline';
  onStyleChange: (style: 'tree' | 'flow' | 'network' | 'timeline') => void;
}

export function VisualizationStyleSelector({
  visualizationStyle,
  onStyleChange,
}: VisualizationStyleSelectorProps) {
  const { t } = useTranslation();

  const styleOptions: {
    value: 'tree' | 'flow' | 'network' | 'timeline';
    label: string;
  }[] = [
    { value: 'tree', label: t('storyDetail.treeView', 'Tree') },
    { value: 'flow', label: t('storyDetail.flowView', 'Flow') },
    { value: 'network', label: t('storyDetail.networkView', 'Network') },
    { value: 'timeline', label: t('storyDetail.timelineView', 'Timeline') },
  ];

  return (
    <View className="mb-4">
      <Text className="text-sm font-medium text-typography-900 dark:text-typography-100 mb-1">
        {t('storyDetail.visualizationStyle', 'Visualization Style')}:
      </Text>
      <View className="flex flex-row flex-wrap">
        {styleOptions.map((option) => (
          <Button
            key={option.value}
            action="secondary"
            variant={visualizationStyle === option.value ? 'solid' : 'outline'}
            size="sm"
            className="mr-2 mb-2"
            onPress={() => onStyleChange(option.value)}
          >
            <ButtonText>{option.label}</ButtonText>
          </Button>
        ))}
      </View>
    </View>
  );
}
