/**
 * M3E 演示模态框
 */

import React from 'react';
import { Modal, View, Pressable } from 'react-native';
import { X } from 'lucide-react-native';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';
import { M3EText } from '@/components/ui/m3e-typography';
import { M3EDemoPage } from '@/features/settings/components/m3e-demo-page';

interface M3EDemoModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function M3EDemoModal({ visible, onClose }: M3EDemoModalProps) {
  const { colors } = useUnifiedTheme();

  return (
    <Modal
      className="flex-1 justify-center items-center bg-black/50"
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        {/* Header */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            paddingVertical: 12,
            borderBottomWidth: 1,
            borderBottomColor: colors.outlineVariant,
            backgroundColor: colors.surface,
          }}
        >
          <M3EText variant="titleLarge">M3E 设计系统演示</M3EText>
          <Pressable
            onPress={onClose}
            style={{
              padding: 8,
              borderRadius: 20,
              backgroundColor: colors.surfaceContainer,
            }}
          >
            <X size={20} color={colors.onSurface} />
          </Pressable>
        </View>

        {/* Content */}
        <M3EDemoPage />
      </View>
    </Modal>
  );
}
