import React from 'react';
import { Text } from '@/components/base';
import { View, Text, TouchableOpacity } from 'react-native';

/**
 * M3E Snackbar 组件的属性接口
 */
export interface M3ESnackbarProps {
  /** 消息文本 */
  message: string;
  /** 是否显示 */
  visible?: boolean;
  /** 操作按钮文本 */
  actionText?: string;
  /** 操作按钮回调 */
  onActionPress?: () => void;
  /** 是否显示关闭按钮 */
  showCloseButton?: boolean;
  /** 关闭回调 */
  onClose?: () => void;
  /** 自动隐藏时间（毫秒），0 表示不自动隐藏 */
  duration?: number;
  /** 是否为多行文本 */
  multiline?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 获取 Snackbar 的样式类
 */
const getSnackbarStyles = (multiline: boolean) => {
  return {
    container: `bg-gray-800 dark:bg-gray-200 rounded shadow-lg mx-4 mb-4 ${
      multiline ? 'min-h-[68px]' : 'h-12'
    }`,
    content: multiline 
      ? 'flex-row items-start justify-between p-4' 
      : 'flex-row items-center justify-between px-4 py-3',
    message: 'text-gray-100 dark:text-gray-900 text-sm flex-1',
    actionButton: 'ml-2 px-3 py-1 rounded-full',
    actionText: 'text-purple-400 dark:text-purple-600 text-sm font-medium',
    closeButton: 'ml-2 p-2 rounded-full',
    closeIcon: 'w-4 h-4 text-gray-100 dark:text-gray-900'
  };
};

/**
 * 关闭图标组件
 */
const CloseIcon: React.FC<{ className?: string }> = ({ className }) => (
  <View className={`${className} items-center justify-center`}>
    <View className="w-3 h-0.5 bg-current rounded-full transform rotate-45 absolute" />
    <View className="w-3 h-0.5 bg-current rounded-full transform -rotate-45 absolute" />
  </View>
);

/**
 * M3E Snackbar 组件
 * 
 * 基于 Material Design 3 规范的消息条组件，用于显示简短的消息和可选的操作。
 * 
 * @example
 * ```tsx
 * const [visible, setVisible] = useState(false);
 * 
 * <M3ESnackbar
 *   visible={visible}
 *   message="This is a snackbar message"
 *   actionText="Action"
 *   onActionPress={() => console.log('Action pressed')}
 *   showCloseButton={true}
 *   onClose={() => setVisible(false)}
 *   duration={4000}
 * />
 * 
 * // 多行消息
 * <M3ESnackbar
 *   visible={visible}
 *   message="This is a longer snackbar message that spans multiple lines"
 *   multiline={true}
 *   actionText="Longer Action"
 *   onActionPress={() => console.log('Action pressed')}
 *   showCloseButton={true}
 *   onClose={() => setVisible(false)}
 * />
 * ```
 */
export const M3ESnackbar: React.FC<M3ESnackbarProps> = ({
  message,
  visible = false,
  actionText,
  onActionPress,
  showCloseButton = false,
  onClose,
  duration = 4000,
  multiline = false,
  className = '',
}) => {
  const styles = getSnackbarStyles(multiline);

  // 自动隐藏逻辑
  React.useEffect(() => {
    if (visible && duration > 0) {
      const timer = setTimeout(() => {
        onClose?.();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [visible, duration, onClose]);

  if (!visible) {
    return null;
  }

  const containerClasses = `${styles.container} ${className}`;

  return (
    <View className={containerClasses}>
      <View className={styles.content}>
        {/* 消息文本 */}
        <Text className={styles.message} numberOfLines={multiline ? undefined : 1}>
          {message}
        </Text>

        {/* 操作区域 */}
        <View className="flex-row items-center">
          {/* 操作按钮 */}
          {actionText && onActionPress && (
            <TouchableOpacity
              className={styles.actionButton}
              onPress={onActionPress}
              activeOpacity={0.7}
            >
              <Text className={styles.actionText}>{actionText}</Text>
            </TouchableOpacity>
          )}

          {/* 关闭按钮 */}
          {showCloseButton && onClose && (
            <TouchableOpacity
              className={styles.closeButton}
              onPress={onClose}
              activeOpacity={0.7}
            >
              <CloseIcon className={styles.closeIcon} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
};

/**
 * M3E Snackbar 变体组件
 */

/**
 * 仅文本的 Snackbar
 */
export const M3ESnackbarTextOnly: React.FC<Omit<M3ESnackbarProps, 'actionText' | 'onActionPress'>> = (props) => (
  <M3ESnackbar {...props} />
);

/**
 * 带操作的 Snackbar
 */
export const M3ESnackbarWithAction: React.FC<Required<Pick<M3ESnackbarProps, 'actionText' | 'onActionPress'>> & M3ESnackbarProps> = (props) => (
  <M3ESnackbar {...props} />
);

/**
 * 带关闭按钮的 Snackbar
 */
export const M3ESnackbarWithClose: React.FC<Required<Pick<M3ESnackbarProps, 'onClose'>> & M3ESnackbarProps> = (props) => (
  <M3ESnackbar {...props} showCloseButton={true} />
);

/**
 * 多行 Snackbar
 */
export const M3ESnackbarMultiline: React.FC<M3ESnackbarProps> = (props) => (
  <M3ESnackbar {...props} multiline={true} />
);

export default M3ESnackbar;
