/**
 * 基础输入组件
 * 替代 Gluestack UI 的 Input 和 Textarea 组件
 */

import React from 'react';
import { Text } from '@/components/base';
import { TextInput, TextInputProps, View, ViewProps } from 'react-native';
import { cn, createVariants } from '@/utils/nativewind-helpers';

// Input 容器组件
const inputVariants = createVariants({
  base: 'flex-row items-center border border-outline-300 dark:border-outline-600 rounded-lg bg-background-0 dark:bg-background-900',
  variants: {
    variant: {
      outline: 'border-outline-300 dark:border-outline-600',
      filled: 'bg-background-100 dark:bg-background-800 border-transparent',
      underlined: 'border-transparent border-b-2 border-b-outline-300 dark:border-b-outline-600 rounded-none',
    },
    size: {
      sm: 'h-8 px-2',
      md: 'h-10 px-3',
      lg: 'h-12 px-4',
    },
    isInvalid: {
      true: 'border-error-500 dark:border-error-400',
    },
    isFocused: {
      true: 'border-primary-500 dark:border-primary-400',
    },
    isDisabled: {
      true: 'opacity-50 bg-background-100 dark:bg-background-800',
    },
  },
  defaultVariants: {
    variant: 'outline',
    size: 'md',
  },
});

// Input 字段组件
const inputFieldVariants = createVariants({
  base: 'flex-1 text-typography-950 dark:text-typography-50 text-base',
  variants: {
    size: {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

export interface InputProps extends ViewProps {
  className?: string;
  variant?: 'outline' | 'filled' | 'underlined';
  size?: 'sm' | 'md' | 'lg';
  isInvalid?: boolean;
  isFocused?: boolean;
  isDisabled?: boolean;
}

export const Input = React.forwardRef<React.ComponentRef<typeof View>, InputProps>(
  function Input({
    className,
    variant,
    size,
    isInvalid,
    isFocused,
    isDisabled,
    ...props
  }, ref) {
    return (
      <View
        ref={ref}
        {...props}
        className={inputVariants({
          variant,
          size,
          isInvalid,
          isFocused,
          isDisabled,
          class: className,
        })}
      />
    );
  }
);

Input.displayName = 'Input';

export interface InputFieldProps extends TextInputProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const InputField = React.forwardRef<React.ComponentRef<typeof TextInput>, InputFieldProps>(
  function InputField({ className, size, ...props }, ref) {
    return (
      <TextInput
        ref={ref}
        {...props}
        className={inputFieldVariants({ size, class: className })}
        placeholderTextColor="#9CA3AF"
      />
    );
  }
);

InputField.displayName = 'InputField';

// Textarea 组件
const textareaVariants = createVariants({
  base: 'border border-outline-300 dark:border-outline-600 rounded-lg bg-background-0 dark:bg-background-900 p-3 text-typography-950 dark:text-typography-50 text-base',
  variants: {
    variant: {
      outline: 'border-outline-300 dark:border-outline-600',
      filled: 'bg-background-100 dark:bg-background-800 border-transparent',
    },
    size: {
      sm: 'min-h-[80px] text-sm',
      md: 'min-h-[100px] text-base',
      lg: 'min-h-[120px] text-lg',
    },
    isInvalid: {
      true: 'border-error-500 dark:border-error-400',
    },
    isFocused: {
      true: 'border-primary-500 dark:border-primary-400',
    },
    isDisabled: {
      true: 'opacity-50 bg-background-100 dark:bg-background-800',
    },
  },
  defaultVariants: {
    variant: 'outline',
    size: 'md',
  },
});

export interface TextareaProps extends TextInputProps {
  className?: string;
  variant?: 'outline' | 'filled';
  size?: 'sm' | 'md' | 'lg';
  isInvalid?: boolean;
  isFocused?: boolean;
  isDisabled?: boolean;
}

export const Textarea = React.forwardRef<React.ComponentRef<typeof TextInput>, TextareaProps>(
  function Textarea({
    className,
    variant,
    size,
    isInvalid,
    isFocused,
    isDisabled,
    ...props
  }, ref) {
    return (
      <TextInput
        ref={ref}
        {...props}
        multiline
        textAlignVertical="top"
        className={textareaVariants({
          variant,
          size,
          isInvalid,
          isFocused,
          isDisabled,
          class: className,
        })}
        placeholderTextColor="#9CA3AF"
      />
    );
  }
);

Textarea.displayName = 'Textarea';
