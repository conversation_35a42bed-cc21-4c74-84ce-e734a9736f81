import React from 'react';
import { Text } from '@/components/base';
import { View, TextInput, TouchableOpacity, Text } from 'react-native';
import { Search, X } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

interface SearchBarProps {
  value?: string;
  onChangeText?: (text: string) => void;
  onSearch?: (text: string) => void;
  autoFocus?: boolean;
  placeholder?: string;
}

export default function SearchBar({
  value,
  onChangeText,
  onSearch,
  autoFocus = false,
  placeholder,
}: SearchBarProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [searchText, setSearchText] = React.useState(value || '');

  // 如果外部 value 变化，更新内部状态
  React.useEffect(() => {
    if (value !== undefined) {
      setSearchText(value);
    }
  }, [value]);

  const handleChangeText = (text: string) => {
    // 如果提供了外部 onChangeText，则调用它
    if (onChangeText) {
      onChangeText(text);
    } else {
      // 否则使用内部状态
      setSearchText(text);
    }
  };

  const handleSubmit = () => {
    const textToSearch = value !== undefined ? value : searchText;
    if (onSearch && textToSearch.trim()) {
      onSearch(textToSearch);
    }
  };

  const handleClear = () => {
    if (onChangeText) {
      onChangeText('');
    }
    setSearchText('');
  };

  return (
    <View className="flex-row items-center h-14 px-1 rounded-full bg-surface-container-high dark:bg-surface-container-high-dark border border-outline-variant dark:border-outline-variant-dark">
      {/* 搜索图标 */}
      <View className="w-10 h-10 items-center justify-center ml-2 mr-3">
        <Search
          className="text-on-surface-variant dark:text-on-surface-variant-dark"
          size={20}
        />
      </View>

      {/* 搜索输入框 */}
      <TextInput
        className="flex-1 text-base text-on-surface dark:text-on-surface-dark py-2"
        placeholder={
          placeholder || t('searchPlaceholder', '搜索故事、作者、标签...')
        }
        placeholderTextColor="#79747E"
        value={value !== undefined ? value : searchText}
        onChangeText={handleChangeText}
        onSubmitEditing={handleSubmit}
        returnKeyType="search"
        autoFocus={autoFocus}
      />

      {/* 清除按钮 */}
      {(value || searchText) && (value || searchText).length > 0 && (
        <TouchableOpacity
          onPress={handleClear}
          className="w-10 h-10 items-center justify-center mr-2 rounded-full"
          activeOpacity={0.7}
        >
          <View className="w-6 h-6 bg-on-surface-variant dark:bg-on-surface-variant-dark rounded-full items-center justify-center">
            <Text className="text-surface dark:text-surface-dark text-xs font-medium">
              ×
            </Text>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
}
