/**
 * 清理未使用的依赖包脚本
 * 
 * 此脚本用于移除项目中未使用的依赖包
 */

const { execSync } = require('child_process');
const fs = require('fs');

// 根据 depcheck 结果，这些是未使用的依赖
const unusedDependencies = [
  '@legendapp/motion',
  '@lucide/lab',
  '@react-navigation/native',
  'expo-blur',
  'expo-camera',
  'expo-haptics',
  'expo-symbols',
  'expo-system-ui',
  'expo-web-browser',
  'i18n-js',
  'react-dom',
  'react-native-css-interop',
  'react-native-dotenv'
];

const unusedDevDependencies = [
  'jscodeshift',
  'node-fetch'
];

// 需要保留的依赖（即使 depcheck 认为未使用）
const keepDependencies = [
  'react-dom', // Web 端需要
  'expo-camera', // 可能在某些功能中需要
  'expo-haptics', // 触觉反馈功能
  'expo-web-browser', // 可能在某些链接打开中需要
];

// 过滤掉需要保留的依赖
const dependenciesToRemove = unusedDependencies.filter(dep => !keepDependencies.includes(dep));

console.log('🧹 开始清理未使用的依赖包...');
console.log('='.repeat(50));

// 移除未使用的依赖
if (dependenciesToRemove.length > 0) {
  console.log('📦 移除未使用的依赖:');
  dependenciesToRemove.forEach(dep => {
    console.log(`   - ${dep}`);
  });
  
  try {
    const command = `pnpm remove ${dependenciesToRemove.join(' ')}`;
    console.log(`\n执行命令: ${command}`);
    execSync(command, { stdio: 'inherit' });
    console.log('✅ 依赖移除成功');
  } catch (error) {
    console.error('❌ 移除依赖时出错:', error.message);
  }
}

// 移除未使用的开发依赖
if (unusedDevDependencies.length > 0) {
  console.log('\n🛠️ 移除未使用的开发依赖:');
  unusedDevDependencies.forEach(dep => {
    console.log(`   - ${dep}`);
  });
  
  try {
    const command = `pnpm remove -D ${unusedDevDependencies.join(' ')}`;
    console.log(`\n执行命令: ${command}`);
    execSync(command, { stdio: 'inherit' });
    console.log('✅ 开发依赖移除成功');
  } catch (error) {
    console.error('❌ 移除开发依赖时出错:', error.message);
  }
}

// 保留的依赖说明
if (keepDependencies.length > 0) {
  console.log('\n🔒 保留的依赖 (有特殊用途):');
  const keepReasons = {
    'react-dom': 'Web 端渲染需要',
    'expo-camera': '相机功能可能需要',
    'expo-haptics': '触觉反馈功能',
    'expo-web-browser': '外部链接打开功能',
  };
  
  keepDependencies.forEach(dep => {
    const reason = keepReasons[dep] || '特殊用途';
    console.log(`   - ${dep} (${reason})`);
  });
}

console.log('\n📊 清理结果:');
console.log(`   移除的依赖: ${dependenciesToRemove.length} 个`);
console.log(`   移除的开发依赖: ${unusedDevDependencies.length} 个`);
console.log(`   保留的依赖: ${keepDependencies.length} 个`);

console.log('\n🎉 依赖清理完成!');
console.log('\n📝 建议的后续操作:');
console.log('1. 运行 pnpm install 确保依赖正确');
console.log('2. 运行应用程序测试功能');
console.log('3. 检查是否有编译错误');
