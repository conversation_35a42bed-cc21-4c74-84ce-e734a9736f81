import React from 'react';
import { View } from 'react-native';

import { Text  } from '@/components/base';

import { Crown } from 'lucide-react-native';

interface FeaturedStoryCardHeaderProps {
  title: string;
  isPremium: boolean;
}

export function FeaturedStoryCardHeader({
  title,
  isPremium,
}: FeaturedStoryCardHeaderProps) {
  return (
    <>
      {isPremium && (
        <View className="flex flex-row absolute top-4 right-4 bg-yellow-400 px-2 py-0.5 rounded-full items-center">
          <Crown size={12} color="#000" />
          <Text className="text-xs font-medium text-black ml-1">会员</Text>
        </View>
      )}
      <Text className="text-2xl font-bold text-white mb-2">{title}</Text>
    </>
  );
}
