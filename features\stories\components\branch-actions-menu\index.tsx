import React, { useState } from 'react';
import { Modal, TextInput, Alert, TouchableOpacity , View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { MoreVertical, Edit, Trash, X } from 'lucide-react-native';
import { StorySegment } from '@/api/stories';

// Import gluestack-ui components

import { Text  } from '@/components/base';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { M3ETextField } from '@/components/ui/m3e-text-field';
import { M3ETextFieldField } from '@/components/ui/m3e-text-field';



import { Pressable } from '@/components/base';

interface BranchActionsMenuProps {
  segment: StorySegment;
  onRename: (
    segmentId: string,
    branchTitle: string
  ) => Promise<StorySegment | null>;
  onDelete: (segmentId: string) => Promise<boolean>;
  isAuthor: boolean;
}

export default function BranchActionsMenu({
  segment,
  onRename,
  onDelete,
  isAuthor,
}: BranchActionsMenuProps) {
  const { t } = useTranslation();

  const [menuVisible, setMenuVisible] = useState(false);
  const [renameModalVisible, setRenameModalVisible] = useState(false);
  const [newBranchTitle, setNewBranchTitle] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // 提取分支标题（如果有）
  const extractBranchTitle = (content: string): string => {
    const titleMatch = content.match(/^\[(.*?)\]\s/);
    return titleMatch ? titleMatch[1] : '';
  };

  // 打开重命名模态框
  const handleOpenRenameModal = () => {
    setNewBranchTitle(extractBranchTitle(segment.content));
    setRenameModalVisible(true);
    setMenuVisible(false);
  };

  // 重命名分支
  const handleRename = async () => {
    if (!newBranchTitle.trim()) {
      Alert.alert(
        t('error', 'Error'),
        t('storyDetail.errors.branchTitleRequired', 'Branch title is required')
      );
      return;
    }

    setIsProcessing(true);
    try {
      const result = await onRename(segment.id, newBranchTitle.trim());
      if (result) {
        setRenameModalVisible(false);
        Alert.alert(
          t('success', 'Success'),
          t('storyDetail.branchRenamed', 'Branch renamed successfully')
        );
      }
    } finally {
      setIsProcessing(false);
    }
  };

  // 删除分支
  const handleDelete = () => {
    Alert.alert(
      t('storyDetail.deleteBranchTitle', 'Delete Branch'),
      t(
        'storyDetail.deleteBranchConfirm',
        'Are you sure you want to delete this branch? This action cannot be undone.'
      ),
      [
        {
          text: t('cancel', 'Cancel'),
          style: 'cancel',
        },
        {
          text: t('delete', 'Delete'),
          style: 'destructive',
          onPress: async () => {
            setIsProcessing(true);
            try {
              const success = await onDelete(segment.id);
              if (success) {
                setMenuVisible(false);
                // 成功消息会在useStoryBranches中处理
              }
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
    setMenuVisible(false);
  };

  // 如果不是作者，不显示菜单
  if (!isAuthor) {
    return null;
  }

  return (
    <>
      <Button
        action="secondary"
        variant="outline"
        size="sm"
        onPress={() => setMenuVisible(true)}
        className="w-9 h-9 rounded-full justify-center items-center"
      >
        <ButtonIcon as={MoreVertical} size={18} />
      </Button>

      {/* 操作菜单 */}
      <Modal visible={menuVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setMenuVisible(false)}
      >
        <Pressable className="flex-1 bg-black/50 justify-center items-center"
          onPress={() => setMenuVisible(false)}
        >
          <View className="w-[70%] bg-background-50 dark:bg-background-900 rounded-md overflow-hidden border border-outline-200 dark:border-outline-700"
            onStartShouldSetResponder={() => true}
            onTouchEnd={(e) => e.stopPropagation()}
          >
            <Button
              action="secondary"
              variant="ghost"
              size="md"
              onPress={handleOpenRenameModal}
              className="flex-row items-center justify-start p-4 border-b border-outline-200 dark:border-outline-700"
            >
              <ButtonIcon as={Edit} size={18} className="mr-3" />
              <ButtonText>
                {t('storyDetail.renameBranch', 'Rename Branch')}
              </ButtonText>
            </Button>

            <Button
              action="danger"
              variant="ghost"
              size="md"
              onPress={handleDelete}
              className="flex-row items-center justify-start p-4"
            >
              <ButtonIcon as={Trash} size={18} className="mr-3" />
              <ButtonText>
                {t('storyDetail.deleteBranch', 'Delete Branch')}
              </ButtonText>
            </Button>
          </View>
        </Pressable>
      </Modal>

      {/* 重命名模态框 */}
      <Modal visible={renameModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setRenameModalVisible(false)}
      >
        <View className="flex justify-center items-center flex-1 bg-black/50 p-4">
          <View className="w-[90%] bg-background-50 dark:bg-background-900 rounded-lg overflow-hidden border border-outline-200 dark:border-outline-700">
            <View className="flex flex-row justify-between items-center p-4 border-b border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900">
              <Text className="text-lg font-bold text-typography-900 dark:text-typography-100">
                {t('storyDetail.renameBranch', 'Rename Branch')}
              </Text>
              <Button
                action="secondary"
                variant="ghost"
                size="sm"
                onPress={() => setRenameModalVisible(false)}
                isDisabled={isProcessing}
                className="w-10 h-10 rounded-full justify-center items-center"
              >
                <ButtonIcon as={X} size={24} />
              </Button>
            </View>

            <View className="px-4 pt-4">
              <Text className="text-base font-medium text-typography-900 dark:text-typography-100 mb-1">
                {t('storyDetail.branchTitle', 'Branch Title')}
              </Text>
              <M3ETextField size="md"
                className="bg-surface-50 dark:bg-surface-900 rounded-md border border-outline-200 dark:border-outline-700"
              >
                <M3ETextField value={newBranchTitle}
                  onChangeText={setNewBranchTitle}
                  placeholder={t(
                    'storyDetail.branchTitlePlaceholder',
                    'Enter branch title'
                  )}
                  editable={!isProcessing}
                  className="text-typography-900 dark:text-typography-100"
                 />
              </M3ETextField>
            </View>

            <View className="flex flex-row justify-end p-4 mt-4 border-t border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900">
              <Button
                action="secondary"
                variant="outline"
                size="md"
                onPress={() => setRenameModalVisible(false)}
                isDisabled={isProcessing}
                className="mr-3 px-4 py-2 rounded-md"
              >
                <ButtonText>{t('cancel', 'Cancel')}</ButtonText>
              </Button>

              <Button
                action="primary"
                variant="solid"
                size="md"
                onPress={handleRename}
                isDisabled={isProcessing}
                className="px-4 py-2 rounded-md"
              >
                <ButtonText>{t('save', 'Save')}</ButtonText>
              </Button>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}
