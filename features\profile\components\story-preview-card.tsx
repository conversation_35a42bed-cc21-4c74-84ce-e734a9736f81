import React from 'react';
import { Image, View } from 'react-native';

import { Text  } from '@/components/base';
import { M3ECard } from '@/components/ui/m3e-card';

interface Story {
  id: string;
  title: string;
  coverImage?: string;
}

interface StoryPreviewCardProps {
  story: Story;
  onPress?: (storyId: string) => void;
}

export function StoryPreviewCard({ story, onPress }: StoryPreviewCardProps) {
  const handlePress = () => {
    if (onPress) {
      onPress(story.id);
    }
  };

  return (
    <M3ECard
      variant="elevated"
      onPress={handlePress}
      style={{ marginRight: 16, width: 150 }}
    >
      <Image
        source={{
          uri: story.coverImage || 'https://via.placeholder.com/150x100',
        }}
        style={{
          width: 150,
          height: 100,
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
        }}
        resizeMode="cover"
      />
      <View className="p-3">
        <Text
          className="text-sm font-medium text-on-surface dark:text-on-surface-dark leading-[18px]"
          numberOfLines={2}
        >
          {story.title}
        </Text>
      </View>
    </M3ECard>
  );
}
