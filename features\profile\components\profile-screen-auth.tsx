import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Button, ButtonText } from '@/components/ui/m3e-button';
import { Text  } from '@/components/base';

interface ProfileScreenAuthProps {
  onLogin: () => void;
  onRegister: () => void;
}

export function ProfileScreenAuth({
  onLogin,
  onRegister,
}: ProfileScreenAuthProps) {
  const { t } = useTranslation();

  return (
    <View className="flex-1 p-6 bg-background-100 justify-center items-center">
      <View className="items-center w-full">
        <Text className="text-center text-lg mb-6">
          {t('profileAuthPrompt')}
        </Text>
        <Button
          action="primary"
          variant="solid"
          size="md"
          onPress={onLogin}
          className="w-4/5 py-3 rounded-xl mb-3 min-h-[48px]"
        >
          <ButtonText>{t('loginButton')}</ButtonText>
        </Button>
        <Button
          action="secondary"
          variant="solid"
          size="md"
          onPress={onRegister}
          className="w-4/5 py-3 rounded-xl mb-3 min-h-[48px]"
        >
          <ButtonText>{t('registerButton')}</ButtonText>
        </Button>
      </View>
    </View>
  );
}
