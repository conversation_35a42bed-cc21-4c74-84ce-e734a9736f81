import React from 'react';
import { FlatList , View } from 'react-native';
import { useTranslation } from 'react-i18next';
import StoryPreviewCard from '@/components/stories/story-preview-card';
import { Story } from '@/types/story';

import { Text  } from '@/components/base';
import { Heading  } from '@/components/base';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { Center } from '@/components/ui/center/index';

interface MyStoriesSectionProps {
  stories: Story[];
  onStoryPress?: (storyId: string) => void;
  loading?: boolean;
  error?: string | null;
}

export function MyStoriesSection({
  stories,
  onStoryPress,
  loading,
  error,
}: MyStoriesSectionProps) {
  const { t } = useTranslation();

  // Mock stories for the UI if none are provided
  const mockStories = [
    {
      id: 'A01',
      title: 'A01',
      coverImage: 'https://picsum.photos/seed/1/200/300',
    },
    {
      id: '001',
      title: '001',
      coverImage: 'https://picsum.photos/seed/2/200/300',
    },
    { id: '1', title: '1', coverImage: 'https://picsum.photos/seed/3/200/300' },
  ];

  const displayStories = stories && stories.length > 0 ? stories : mockStories;

  // Render a single story preview item
  const renderStoryItem = ({ item }: { item: any }) => (
    <StoryPreviewCard story={item} onPress={() => onStoryPress?.(item.id)} />
  );

  return (
    <View className="bg-background-950 pt-4 pb-8">
      <View className="px-4 mb-4">
        <Heading size="md" className="text-typography-50 font-bold">
          {t('profile.myStories', 'My Stories')}
        </Heading>
      </View>

      {loading && (
        <View className="flex justify-center items-center my-4">
          <M3EProgressIndicator size="large" color="#0891b2" />
        </View>
      )}

      {!loading && error && (
        <Text className="text-typography-400 text-center px-4">{error}</Text>
      )}

      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={displayStories}
        renderItem={renderStoryItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingHorizontal: 16, paddingVertical: 12 }}
        ItemSeparatorComponent={() => <View className="w-4" />}
      />
    </View>
  );
}
