import React, { useState } from 'react';
import { Text } from '@/components/base';
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

import { createStyles } from './title-input-step.styles';
import AiSuggestionCard from '@/components/creation/ai-suggestion-card';
import { Brain } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

interface TitleInputStepProps {
  title: string;
  onTitleChange: (text: string) => void;
  // Add props for AI suggestions if fetched from backend
  // aiSuggestions?: string[]; 
  // onFetchAiSuggestions?: () => void;
}

export function TitleInputStep({ title, onTitleChange }: TitleInputStepProps) {
  const { mode } = useUnifiedTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const [showAiSuggestions, setShowAiSuggestions] = useState(false);

  // Mock AI suggestions for now
  const mockAiSuggestions = [
    t('mockSuggestionTitle1'),
    t('mockSuggestionTitle2'),
    t('mockSuggestionTitle3'),
  ];

  const handleSuggestionSelect = (suggestion: string) => {
    onTitleChange(suggestion);
    setShowAiSuggestions(false); // Hide suggestions after selection
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('nameYourStoryTitle')}</Text>
      <Text style={styles.description}>{t('nameYourStoryDesc')}</Text>
      
      <TextInput
        style={styles.input}
        placeholder={t('storyTitlePlaceholder')}
        placeholderTextColor={colors.placeholder} // Use theme placeholder color
        value={title}
        onChangeText={onTitleChange}
        maxLength={50} // Keep max length consistent
      />
      
      <TouchableOpacity 
        style={styles.aiButton}
        onPress={() => setShowAiSuggestions(!showAiSuggestions)}
      >
        <Brain color={colors.primary} size={20} />
        <Text style={styles.aiButtonText}>{t('getAiTitleSuggestions')}</Text>
      </TouchableOpacity>
      
      {showAiSuggestions && (
        <View style={styles.suggestionsContainer}>
          {mockAiSuggestions.map((suggestion, index) => (
            <AiSuggestionCard 
              key={index} // Using index as key for mock data
              suggestion={suggestion} 
              onSelect={handleSuggestionSelect}
            />
          ))}
        </View>
      )}
    </View>
  );
} 