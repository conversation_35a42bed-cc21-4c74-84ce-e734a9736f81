import React from 'react';
import { Button, ButtonText } from '@/components/base';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';

interface ActionButtonsProps {
  isLoggedIn: boolean;
  showContinueInput: boolean;
  onToggleContinueInput: () => void;
}

export default function ActionButtons({
  isLoggedIn,
  showContinueInput,
  onToggleContinueInput,
}: ActionButtonsProps) {
  const { t } = useTranslation();

  return (
    <View className="flex-row justify-end items-center mt-1 border-t border-outline-200 dark:border-outline-700 pt-2">
      {/* Placeholder for Like Button */}
      <Button action="secondary" variant="link" size="sm" className="ml-3">
        <ButtonText className="text-primary-600 dark:text-primary-400">
          Like (TODO)
        </ButtonText>
      </Button>

      {/* Placeholder for Comment Button */}
      <Button action="secondary" variant="link" size="sm" className="ml-3">
        <ButtonText className="text-primary-600 dark:text-primary-400">
          Comment (TODO)
        </ButtonText>
      </Button>

      {/* Continue Button */}
      {isLoggedIn && (
        <Button
          action="secondary"
          variant="link"
          size="sm"
          onPress={onToggleContinueInput}
          className="ml-3"
        >
          <ButtonText className="text-primary-600 dark:text-primary-400">
            {showContinueInput
              ? t('story.cancelContinue', 'Cancel')
              : t('story.continue', 'Continue Story...')}
          </ButtonText>
        </Button>
      )}
    </View>
  );
}
