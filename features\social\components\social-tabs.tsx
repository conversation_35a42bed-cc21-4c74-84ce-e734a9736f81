import React, { useEffect, useState } from 'react';
import { View } from 'react-native';



import { Text  } from '@/components/base';
import { Pressable } from '@/components/base';
import { Divider } from '@/components/ui/divider';
import { Bell, MessageSquare, Search, TrendingUp } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';
import NotificationBadge from '@/features/notifications/components/notification-badge';
import MessageBadge from '@/features/messages/components/message-badge';
import { getUnreadNotificationCount } from '@/api/notifications';
import { getUnreadMessageCount } from '@/api/messages';

export type SocialTabKey = 'feed' | 'discover' | 'messages' | 'notifications';

interface SocialTabsProps {
  activeTab: SocialTabKey;
  onTabPress: (tabKey: SocialTabKey) => void;
}

const TABS: SocialTabKey[] = ['feed', 'discover', 'messages', 'notifications'];

const renderTabIcon = (
  tabKey: SocialTabKey,
  active: boolean,
  isDark: boolean,
  unreadNotificationCount: number = 0,
  unreadMessageCount: number = 0
) => {
  const iconColor = active
    ? isDark
      ? 'primary.400'
      : 'primary.500'
    : isDark
    ? 'typography-400'
    : 'typography-500';
  const size = 22;

  switch (tabKey) {
    case 'feed':
      return <TrendingUp size={size} className={`text-${iconColor}`} />;
    case 'discover':
      return <Search size={size} className={`text-${iconColor}`} />;
    case 'messages':
      return (
        <View className="relative">
          <MessageSquare size={size} className={`text-${iconColor}`} />
          {unreadMessageCount > 0 && tabKey === 'messages' && (
            <MessageBadge count={unreadMessageCount} size="small" />
          )}
        </View>
      );
    case 'notifications':
      return (
        <View className="relative">
          <Bell size={size} className={`text-${iconColor}`} />
          {unreadNotificationCount > 0 && tabKey === 'notifications' && (
            <NotificationBadge count={unreadNotificationCount} size="small" />
          )}
        </View>
      );
    default:
      return null;
  }
};

export function SocialTabs({ activeTab, onTabPress }: SocialTabsProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [unreadNotificationCount, setUnreadNotificationCount] = useState(0);
  const [unreadMessageCount, setUnreadMessageCount] = useState(0);

  // 获取未读通知数量
  useEffect(() => {
    const fetchUnreadNotificationCount = async () => {
      try {
        const { count } = await getUnreadNotificationCount();
        setUnreadNotificationCount(count);
      } catch (error) {
        console.error('Error fetching unread notification count:', error);
      }
    };

    fetchUnreadNotificationCount();

    // 设置定时器，每分钟刷新一次
    const intervalId = setInterval(fetchUnreadNotificationCount, 60000);

    return () => clearInterval(intervalId);
  }, []);

  // 获取未读消息数量
  useEffect(() => {
    const fetchUnreadMessageCount = async () => {
      try {
        const { count } = await getUnreadMessageCount();
        setUnreadMessageCount(count);
      } catch (error) {
        console.error('Error fetching unread message count:', error);
      }
    };

    fetchUnreadMessageCount();

    // 设置定时器，每分钟刷新一次
    const intervalId = setInterval(fetchUnreadMessageCount, 60000);

    return () => clearInterval(intervalId);
  }, []);

  const getTabLabel = (tabKey: SocialTabKey): string => {
    switch (tabKey) {
      case 'feed':
        return t('social.tabs.feed', '动态');
      case 'discover':
        return t('social.tabs.discover', '发现');
      case 'messages':
        return t('social.tabs.messages', '消息');
      case 'notifications':
        return t('social.tabs.notifications', '通知');
      default:
        return '';
    }
  };

  return (
    <View className={`border-b ${
        isDark
          ? 'border-outline-700 bg-background-950'
          : 'border-outline-200 bg-background-50'
      }`}
    >
      <View className="flex flex-row justify-around py-2">
        {TABS.map((tabKey) => (
          <Pressable key={tabKey}
            className={`px-4 py-2 items-center justify-center border-b-2 ${
              activeTab === tabKey
                ? isDark
                  ? 'border-primary-400'
                  : 'border-primary-500'
                : 'border-transparent'
            }`}
            onPress={() => onTabPress(tabKey)}
            accessibilityLabel={getTabLabel(tabKey)}
            accessibilityRole="tab"
            accessibilityState={{ selected: activeTab === tabKey }}
          >
            {renderTabIcon(
              tabKey,
              activeTab === tabKey,
              isDark,
              unreadNotificationCount,
              unreadMessageCount
            )}
            <Text
              className={`text-xs mt-1 ${
                activeTab === tabKey
                  ? isDark
                    ? 'text-primary-400 font-medium'
                    : 'text-primary-500 font-medium'
                  : isDark
                  ? 'text-typography-400 font-normal'
                  : 'text-typography-500 font-normal'
              }`}
            >
              {getTabLabel(tabKey)}
            </Text>
          </Pressable>
        ))}
      </View>
    </View>
  );
}
