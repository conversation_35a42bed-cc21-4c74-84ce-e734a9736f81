import React from 'react';
import { Text } from '@/components/base';
import { View, Text, TouchableOpacity } from 'react-native';

// Checkbox 的属性接口
export interface M3ECheckboxProps {
  /** 是否选中 */
  checked?: boolean;
  /** 是否为不确定状态 */
  indeterminate?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否为错误状态 */
  error?: boolean;
  /** 标签文本 */
  label?: string;
  /** 值变化事件 */
  onValueChange?: (checked: boolean) => void;
  /** 自定义样式类名 */
  className?: string;
}

// 样式化的复选框
/**
 * 获取复选框的样式类
 */
const getCheckboxStyles = (
  checked: boolean,
  indeterminate: boolean,
  error: boolean,
  disabled: boolean
) => {
  const baseClasses =
    'w-[18px] h-[18px] rounded-sm border-2 items-center justify-center';

  if (disabled) {
    if (checked || indeterminate) {
      return `${baseClasses} bg-gray-400 border-gray-400`;
    }
    return `${baseClasses} border-gray-400`;
  }

  if (error) {
    if (checked || indeterminate) {
      return `${baseClasses} bg-red-600 border-red-600`;
    }
    return `${baseClasses} border-red-600`;
  }

  if (checked || indeterminate) {
    return `${baseClasses} bg-purple-600 border-purple-600`;
  }

  return `${baseClasses} border-gray-600 dark:border-gray-400`;
};

/**
 * 渲染复选框图标
 */
const renderCheckboxIcon = (checked: boolean, indeterminate: boolean) => {
  if (indeterminate) {
    return <View className="w-3 h-0.5 bg-white rounded-full" />;
  }

  if (checked) {
    return (
      <View className="w-3 h-3 items-center justify-center">
        <View className="w-2 h-1 border-l-2 border-b-2 border-white transform rotate-[-45deg] translate-y-[-1px]" />
      </View>
    );
  }

  return null;
};

/**
 * M3E Checkbox 组件
 *
 * 基于 Material Design 3 规范的复选框组件，支持选中、不确定和错误状态。
 *
 * @example
 * ```tsx
 * const [checked, setChecked] = useState(false);
 *
 * <M3ECheckbox
 *   checked={checked}
 *   onValueChange={setChecked}
 *   label="同意条款和条件"
 * />
 *
 * // 不确定状态
 * <M3ECheckbox
 *   indeterminate={true}
 *   label="部分选中"
 * />
 *
 * // 错误状态
 * <M3ECheckbox
 *   checked={false}
 *   error={true}
 *   label="必填项"
 * />
 * ```
 */
export const M3ECheckbox: React.FC<M3ECheckboxProps> = ({
  checked = false,
  indeterminate = false,
  disabled = false,
  error = false,
  label,
  onValueChange,
  className = '',
}) => {
  const handlePress = () => {
    if (disabled) return;
    onValueChange?.(!checked);
  };

  const checkboxStyles = getCheckboxStyles(
    checked,
    indeterminate,
    error,
    disabled
  );
  const containerClasses = `flex-row items-center gap-3 ${className}`;

  return (
    <TouchableOpacity
      disabled={disabled}
      onPress={handlePress}
      className={containerClasses}
      activeOpacity={disabled ? 1 : 0.7}
    >
      {/* 复选框 */}
      <View className="p-3">
        <View className={checkboxStyles}>
          {renderCheckboxIcon(checked, indeterminate)}
        </View>
      </View>

      {/* 标签 */}
      {label && (
        <Text
          className={`flex-1 text-base ${
            disabled
              ? 'text-gray-400'
              : error
              ? 'text-red-600'
              : 'text-gray-900 dark:text-white'
          }`}
        >
          {label}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default M3ECheckbox;
