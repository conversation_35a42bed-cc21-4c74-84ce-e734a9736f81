import React from 'react';
import { View } from 'react-native';

import { Text  } from '@/components/base';
import { Pressable } from '@/components/base';

import { ScrollView } from '@/components/ui/scroll-view';
import { useTranslation } from 'react-i18next';
import { RankingPeriod } from '@/api/rankings';

interface RankingTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  activePeriod: RankingPeriod;
  onPeriodChange: (period: RankingPeriod) => void;
}

export function RankingTabs({
  activeTab,
  onTabChange,
  activePeriod,
  onPeriodChange,
}: RankingTabsProps) {
  const { t } = useTranslation();

  const tabs = [
    { id: 'stories', label: t('rankings.tabs.stories', '热门故事') },
    { id: 'authors', label: t('rankings.tabs.authors', '热门作者') },
  ];

  const periods = [
    { id: 'day', label: t('rankings.periods.day', '日榜') },
    { id: 'week', label: t('rankings.periods.week', '周榜') },
    { id: 'month', label: t('rankings.periods.month', '月榜') },
    { id: 'all', label: t('rankings.periods.all', '总榜') },
  ];

  return (
    <View className="bg-surface-50 dark:bg-surface-900 pt-4">
      {/* Main Tabs */}
      <View className="flex flex-row px-4">
        {tabs.map((tab) => (
          <Pressable key={tab.id}
            className={`flex-1 py-2 items-center ${
              activeTab === tab.id
                ? 'border-b-2 border-primary-500'
                : 'border-b-2 border-transparent'
            }`}
            onPress={() => onTabChange(tab.id)}
          >
            <Text
              className={`text-base font-medium ${
                activeTab === tab.id
                  ? 'text-primary-500'
                  : 'text-typography-500 dark:text-typography-400'
              }`}
            >
              {tab.label}
            </Text>
          </Pressable>
        ))}
      </View>

      {/* Period Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerClassName="px-4 py-2"
      >
        {periods.map((period) => (
          <Pressable key={period.id}
            className={`px-4 py-1 mr-2 rounded-full ${
              activePeriod === period.id
                ? 'bg-primary-500'
                : 'bg-background-100 dark:bg-background-800'
            }`}
            onPress={() => onPeriodChange(period.id as RankingPeriod)}
          >
            <Text
              className={`text-sm font-medium ${
                activePeriod === period.id
                  ? 'text-white dark:text-black'
                  : 'text-typography-500 dark:text-typography-400'
              }`}
            >
              {period.label}
            </Text>
          </Pressable>
        ))}
      </ScrollView>
    </View>
  );
}
