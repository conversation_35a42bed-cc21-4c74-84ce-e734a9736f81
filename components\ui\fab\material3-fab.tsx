'use client';
import React from 'react';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { cn } from '@/utils/nativewind-helpers';

// Material 3 FAB Props
interface M3FabProps extends React.ComponentPropsWithoutRef<typeof Pressable> {
  variant?: 'primary' | 'secondary' | 'surface' | 'tertiary';
  size?: 'small' | 'medium' | 'large';
  extended?: boolean;
  loading?: boolean;
  children?: React.ReactNode;
}

// Helper function to get FAB styles
const getFabStyles = (
  variant: M3FabProps['variant'] = 'primary',
  size: M3FabProps['size'] = 'medium',
  extended: boolean = false,
  disabled: boolean = false,
  loading: boolean = false
) => {
  const baseClasses =
    'flex items-center justify-center relative overflow-hidden transition-all duration-200 ease-out active:scale-95 shadow-lg';

  // Variant styles
  const variantClasses = {
    primary: 'bg-primary',
    secondary: 'bg-secondary',
    surface: 'bg-surface',
    tertiary: 'bg-tertiary',
  };

  // Size styles
  const sizeClasses = extended
    ? {
        small: 'h-10 px-3 rounded-xl',
        medium: 'h-14 px-4 rounded-2xl',
        large: 'h-16 px-6 rounded-3xl',
      }
    : {
        small: 'w-10 h-10 rounded-xl',
        medium: 'w-14 h-14 rounded-2xl',
        large: 'w-24 h-24 rounded-3xl',
      };

  // State styles
  const stateClasses = disabled || loading ? 'opacity-38' : '';

  return cn(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    extended && 'flex-row gap-2 min-w-20',
    stateClasses
  );
};

// Helper function to get text styles
const getTextStyles = (
  variant: M3FabProps['variant'] = 'primary',
  size: M3FabProps['size'] = 'medium'
) => {
  const baseClasses = 'font-medium leading-tight';

  const variantClasses = {
    primary: 'text-primary-foreground',
    secondary: 'text-secondary-foreground',
    surface: 'text-surface-foreground',
    tertiary: 'text-tertiary-foreground',
  };

  const sizeClasses = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base font-semibold',
  };

  return cn(baseClasses, variantClasses[variant], sizeClasses[size]);
};

// Helper function to get icon styles
const getIconStyles = (
  variant: M3FabProps['variant'] = 'primary',
  size: M3FabProps['size'] = 'medium'
) => {
  const baseClasses = 'flex-shrink-0';

  const variantClasses = {
    primary: 'text-primary-foreground',
    secondary: 'text-secondary-foreground',
    surface: 'text-surface-foreground',
    tertiary: 'text-tertiary-foreground',
  };

  const sizeClasses = {
    small: 'w-5 h-5',
    medium: 'w-6 h-6',
    large: 'w-8 h-8',
  };

  return cn(baseClasses, variantClasses[variant], sizeClasses[size]);
};

// Material 3 FAB Component
const M3Fab = React.forwardRef<React.ElementRef<typeof Pressable>, M3FabProps>(
  function M3Fab(
    {
      className,
      variant = 'primary',
      size = 'medium',
      extended = false,
      loading = false,
      disabled,
      children,
      ...props
    },
    ref
  ) {
    return (
      <Pressable
        ref={ref}
        className={cn(
          getFabStyles(variant, size, extended, disabled, loading),
          className
        )}
        disabled={disabled || loading}
        {...props}
      >
        {loading ? (
          <ActivityIndicator
            size={size === 'small' ? 'small' : 'large'}
            className={getIconStyles(variant, size)}
          />
        ) : (
          children
        )}
      </Pressable>
    );
  }
);

// Material 3 FAB Text Component (for Extended FAB)
interface M3FabTextProps extends React.ComponentPropsWithoutRef<typeof Text> {
  variant?: M3FabProps['variant'];
  size?: M3FabProps['size'];
}

const M3FabText = React.forwardRef<
  React.ElementRef<typeof Text>,
  M3FabTextProps
>(function M3FabText(
  { className, variant = 'primary', size = 'medium', ...props },
  ref
) {
  return (
    <Text
      ref={ref}
      className={cn(getTextStyles(variant, size), className)}
      {...props}
    />
  );
});

// Material 3 FAB Icon Component
interface M3FabIconProps extends React.ComponentPropsWithoutRef<typeof View> {
  variant?: M3FabProps['variant'];
  size?: M3FabProps['size'];
  as?: React.ElementType;
}

const M3FabIcon = React.forwardRef<
  React.ElementRef<typeof View>,
  M3FabIconProps
>(function M3FabIcon(
  { className, variant = 'primary', size = 'medium', as, ...props },
  ref
) {
  if (as) {
    const AsComp = as;
    return (
      <AsComp
        ref={ref}
        className={cn(getIconStyles(variant, size), className)}
        {...props}
      />
    );
  }

  return (
    <View
      ref={ref}
      className={cn(getIconStyles(variant, size), className)}
      {...props}
    />
  );
});

// Export components
M3Fab.displayName = 'M3Fab';
M3FabText.displayName = 'M3FabText';
M3FabIcon.displayName = 'M3FabIcon';

export {
  M3Fab,
  M3FabText,
  M3FabIcon,
  type M3FabProps,
  type M3FabTextProps,
  type M3FabIconProps,
};
