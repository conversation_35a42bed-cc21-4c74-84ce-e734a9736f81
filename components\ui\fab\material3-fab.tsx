'use client';
import React from 'react';
import { Text } from '@/components/base';
import { cssInterop } from 'nativewind';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import type { VariantProps } from '@gluestack-ui/nativewind-utils';
const SCOPE = 'M3_FAB';

const Root = withStyleContext(Pressable, SCOPE);

const UIM3Fab = createButton({
  Root: Root,
  Text,
  Group: View,
  Spinner: ActivityIndicator,
  Icon: UIIcon,
});

cssInterop(PrimitiveIcon, {
  className: {
    target: 'style',
    nativeStyleToProp: {
      height: true,
      width: true,
      fill: true,
      color: 'classNameColor',
      stroke: true,
    },
  },
});

// Material 3 FAB Styles - 符合 M3 Expressive 规范
const m3FabStyle = tva({
  base: 'group/m3-fab flex items-center justify-center relative overflow-hidden transition-all duration-200 ease-out active:scale-95 shadow-lg elevation-3',
  variants: {
    // Material 3 FAB 类型
    variant: {
      primary: 'bg-m3-primary-container', // Primary FAB
      secondary: 'bg-m3-secondary-container', // Secondary FAB
      surface: 'bg-m3-surface-container-high', // Surface FAB
      tertiary: 'bg-m3-tertiary-container', // Tertiary FAB
    },
    // Material 3 FAB 尺寸规范
    size: {
      small: 'w-10 h-10 rounded-xl', // 40dp
      medium: 'w-14 h-14 rounded-2xl', // 56dp - Standard FAB
      large: 'w-24 h-24 rounded-3xl', // 96dp - Large FAB
    },
    // 按钮状态
    state: {
      default: '',
      hovered: 'shadow-xl elevation-4',
      pressed: 'shadow-md elevation-2',
      focused: 'ring-2 ring-m3-primary-main ring-opacity-50',
      disabled: 'opacity-38',
    },
    // 图标位置 (Extended FAB)
    iconPosition: {
      leading: '',
      trailing: 'flex-row-reverse',
      none: '',
    },
    // 是否为扩展FAB
    extended: {
      true: 'flex-row px-4 min-w-20 w-auto h-14 rounded-2xl',
      false: '',
    },
  },
  compoundVariants: [
    // Primary FAB variants
    {
      variant: 'primary',
      state: 'hovered',
      class: 'bg-m3-primary-container shadow-2xl elevation-5',
    },
    {
      variant: 'primary',
      state: 'pressed',
      class: 'bg-m3-primary-container shadow-sm elevation-1',
    },
    // Secondary FAB variants
    {
      variant: 'secondary',
      state: 'hovered',
      class: 'bg-m3-secondary-container shadow-2xl elevation-5',
    },
    {
      variant: 'secondary',
      state: 'pressed',
      class: 'bg-m3-secondary-container shadow-sm elevation-1',
    },
    // Surface FAB variants
    {
      variant: 'surface',
      state: 'hovered',
      class: 'bg-m3-surface-container-highest shadow-2xl elevation-5',
    },
    {
      variant: 'surface',
      state: 'pressed',
      class: 'bg-m3-surface-container shadow-sm elevation-1',
    },
    // Extended FAB specific styling
    {
      extended: true,
      iconPosition: 'leading',
      class: 'gap-2',
    },
    {
      extended: true,
      iconPosition: 'trailing',
      class: 'gap-2',
    },
  ],
  defaultVariants: {
    variant: 'primary',
    size: 'medium',
    state: 'default',
    iconPosition: 'none',
    extended: false,
  },
});

// Material 3 FAB Text Styles (for Extended FAB)
const m3FabTextStyle = tva({
  base: 'font-medium leading-tight web:select-none',
  parentVariants: {
    variant: {
      primary: 'text-m3-primary-on-container',
      secondary: 'text-m3-secondary-on-container',
      surface: 'text-m3-surface-on-container-high',
      tertiary: 'text-m3-tertiary-on-container',
    },
    size: {
      small: 'text-xs font-medium',
      medium: 'text-sm font-medium',
      large: 'text-base font-semibold',
    },
    state: {
      default: '',
      hovered: '',
      pressed: '',
      focused: '',
      disabled: 'opacity-38',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'medium',
    state: 'default',
  },
});

// Material 3 FAB Icon Styles
const m3FabIconStyle = tva({
  base: 'flex-shrink-0',
  parentVariants: {
    variant: {
      primary: 'text-m3-primary-on-container',
      secondary: 'text-m3-secondary-on-container',
      surface: 'text-m3-surface-on-container-high',
      tertiary: 'text-m3-tertiary-on-container',
    },
    size: {
      small: 'w-5 h-5',
      medium: 'w-6 h-6',
      large: 'w-8 h-8',
    },
    state: {
      default: '',
      hovered: '',
      pressed: '',
      focused: '',
      disabled: 'opacity-38',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'medium',
    state: 'default',
  },
});

// Material 3 FAB Spinner Styles
const m3FabSpinnerStyle = tva({
  base: 'flex-shrink-0',
  parentVariants: {
    variant: {
      primary: 'color-m3-primary-on-container',
      secondary: 'color-m3-secondary-on-container',
      surface: 'color-m3-surface-on-container-high',
      tertiary: 'color-m3-tertiary-on-container',
    },
    size: {
      small: 'w-5 h-5',
      medium: 'w-6 h-6',
      large: 'w-8 h-8',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'medium',
  },
});

// TypeScript interfaces
type IM3FabProps = Omit<
  React.ComponentPropsWithoutRef<typeof UIM3Fab>,
  'context'
> &
  VariantProps<typeof m3FabStyle> & {
    className?: string;
    loading?: boolean;
  };

// Material 3 FAB Component
const M3Fab = React.forwardRef<React.ElementRef<typeof UIM3Fab>, IM3FabProps>(
  function M3Fab(
    {
      className,
      variant = 'primary',
      size = 'medium',
      state = 'default',
      iconPosition = 'none',
      extended = false,
      loading = false,
      disabled,
      ...props
    },
    ref
  ) {
    const finalState = disabled ? 'disabled' : state;

    return (
      <UIM3Fab
        ref={ref}
        className={m3FabStyle({
          variant,
          size,
          state: finalState,
          iconPosition,
          extended,
          class: className,
        })}
        disabled={disabled || loading}
        context={{ variant, size, state: finalState, iconPosition, extended }}
        {...props}
      />
    );
  }
);

// Material 3 FAB Text Component (for Extended FAB)
type IM3FabTextProps = React.ComponentPropsWithoutRef<typeof UIM3Fab.Text> &
  VariantProps<typeof m3FabTextStyle> & { className?: string };

const M3FabText = React.forwardRef<
  React.ElementRef<typeof UIM3Fab.Text>,
  IM3FabTextProps
>(function M3FabText({ className, variant, size, state, ...props }, ref) {
  const {
    variant: parentVariant,
    size: parentSize,
    state: parentState,
  } = useStyleContext(SCOPE);

  return (
    <UIM3Fab.Text
      ref={ref}
      className={m3FabTextStyle({
        variant: variant ?? parentVariant,
        size: size ?? parentSize,
        state: state ?? parentState,
        class: className,
      })}
      {...props}
    />
  );
});

// Material 3 FAB Icon Component
type IM3FabIcon = React.ComponentPropsWithoutRef<typeof UIM3Fab.Icon> &
  VariantProps<typeof m3FabIconStyle> & {
    className?: string | undefined;
    as?: React.ElementType;
    height?: number;
    width?: number;
  };

const M3FabIcon = React.forwardRef<
  React.ElementRef<typeof UIM3Fab.Icon>,
  IM3FabIcon
>(function M3FabIcon({ className, size, variant, state, ...props }, ref) {
  const {
    variant: parentVariant,
    size: parentSize,
    state: parentState,
  } = useStyleContext(SCOPE);

  if (typeof props.as === 'string') {
    const AsComp = props.as as React.ElementType;
    return (
      <AsComp
        ref={ref}
        className={m3FabIconStyle({
          variant: variant ?? parentVariant,
          size: size ?? parentSize,
          state: state ?? parentState,
          class: className,
        })}
        {...props}
      />
    );
  }
  return (
    <UIM3Fab.Icon
      ref={ref}
      className={m3FabIconStyle({
        variant: variant ?? parentVariant,
        size: size ?? parentSize,
        state: state ?? parentState,
        class: className,
      })}
      {...props}
    />
  );
});

// Material 3 FAB Spinner Component
type IM3FabSpinnerProps = React.ComponentPropsWithoutRef<
  typeof UIM3Fab.Spinner
> &
  VariantProps<typeof m3FabSpinnerStyle> & { className?: string };

const M3FabSpinner = React.forwardRef<
  React.ElementRef<typeof UIM3Fab.Spinner>,
  IM3FabSpinnerProps
>(function M3FabSpinner({ className, variant, size, ...props }, ref) {
  const { variant: parentVariant, size: parentSize } = useStyleContext(SCOPE);

  return (
    <UIM3Fab.Spinner
      ref={ref}
      className={m3FabSpinnerStyle({
        variant: variant ?? parentVariant,
        size: size ?? parentSize,
        class: className,
      })}
      {...props}
    />
  );
});

// Export components
M3Fab.displayName = 'M3Fab';
M3FabText.displayName = 'M3FabText';
M3FabIcon.displayName = 'M3FabIcon';
M3FabSpinner.displayName = 'M3FabSpinner';

export {
  M3Fab,
  M3FabText,
  M3FabIcon,
  M3FabSpinner,
  type IM3FabProps,
  type IM3FabTextProps,
  type IM3FabIcon as IM3FabIconProps,
  type IM3FabSpinnerProps,
};
