import React, { useState } from 'react';
import { Alert , View } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { updatePassword } from '@/api/auth';
import { useTranslation } from 'react-i18next';

import { Text  } from '@/components/base';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/m3e-button';
import { M3ETextField, M3ETextFieldField } from '@/components/ui/m3e-text-field';

/**
 * NewPasswordScreen component
 *
 * This screen allows users to set a new password after receiving a password reset link.
 * It is accessed via a deep link from the reset password email.
 */
export default function NewPasswordScreen() {
  const { t } = useTranslation();
  const router = useRouter();

  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);

  /**
   * Handles the password update
   */
  const handleUpdatePassword = async () => {
    // Validate passwords
    if (!newPassword || !confirmPassword) {
      Alert.alert(t('auth.errorTitle'), t('auth.fieldRequired'));
      return;
    }

    if (newPassword.length < 6) {
      Alert.alert(t('auth.errorTitle'), t('auth.passwordTooShort'));
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert(t('auth.errorTitle'), t('auth.passwordsDoNotMatch'));
      return;
    }

    setLoading(true);
    try {
      const { error } = await updatePassword(newPassword);
      setLoading(false);

      if (error) {
        console.error('Update password error:', error);
        Alert.alert(
          t('auth.errorTitle'),
          error.message || t('auth.updatePasswordFailed')
        );
      } else {
        Alert.alert(t('auth.successTitle'), t('auth.updatePasswordSuccess'));

        // Since we don't get session data anymore, just redirect to login
        router.replace('/(auth)/login');
      }
    } catch (error) {
      setLoading(false);
      console.error('Update password error:', error);
      Alert.alert(t('auth.errorTitle'), t('auth.networkError'));
    }
  };

  return (
    <View className="flex-1 p-6 bg-background-50 dark:bg-background-900 justify-center">
      <Text className="text-xl font-bold text-typography-900 dark:text-typography-100 mb-6 text-center">
        {t('auth.newPasswordTitle')}
      </Text>

      <Text className="text-base text-typography-700 dark:text-typography-300 mb-8 text-center">
        {t('auth.newPasswordInstructions')}
      </Text>

      <M3ETextField className="w-full mb-4">
        <M3ETextField placeholder={t('auth.newPasswordLabel')}
          secureTextEntry
          value={newPassword}
          onChangeText={setNewPassword}
          className="p-3 border border-outline-300 dark:border-outline-600 rounded-md bg-background-50 dark:bg-background-800 text-typography-900 dark:text-typography-100"
         />
      </M3ETextField>

      <M3ETextField className="w-full mb-6">
        <M3ETextField placeholder={t('auth.confirmPasswordLabel')}
          secureTextEntry
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          className="p-3 border border-outline-300 dark:border-outline-600 rounded-md bg-background-50 dark:bg-background-800 text-typography-900 dark:text-typography-100"
         />
      </M3ETextField>

      <Button
        action="primary"
        variant="solid"
        size="md"
        onPress={handleUpdatePassword}
        isDisabled={loading}
        className="w-full py-3 rounded-md mt-3 mb-4 min-h-[50px] justify-center"
      >
        {loading ? (
          <ButtonSpinner color="$background" />
        ) : (
          <ButtonText>{t('auth.submitButton')}</ButtonText>
        )}
      </Button>
    </View>
  );
}
