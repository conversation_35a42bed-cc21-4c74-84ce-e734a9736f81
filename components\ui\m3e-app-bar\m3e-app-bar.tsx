import React from 'react';
import { Text } from '@/components/base';
import { View, Text, TouchableOpacity, Image, ImageSourcePropType } from 'react-native';


// App Bar 的属性接口
export interface M3EAppBarProps {
  /** 标题 */
  title?: string;
  /** 副标题 */
  subtitle?: string;
  /** 左侧导航图标 */
  navigationIcon?: React.ReactNode;
  /** 左侧导航图标点击事件 */
  onNavigationPress?: () => void;
  /** 右侧操作按钮列表 */
  actions?: React.ReactNode[];
  /** 头像配置 */
  avatar?: {
    source?: ImageSourcePropType;
    monogram?: string;
    onPress?: () => void;
  };
  /** 缩略图 */
  thumbnail?: ImageSourcePropType;
  /** App Bar 尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 标题对齐方式 */
  titleAlignment?: 'left' | 'center';
  /** 配置类型 */
  variant?: 'default' | 'search' | 'image' | 'centered';
  /** 是否有阴影 */
  elevated?: boolean;
  /** 搜索配置 */
  searchConfig?: {
    placeholder?: string;
    value?: string;
    onChangeText?: (text: string) => void;
    onSubmit?: () => void;
  };
  /** 自定义样式类名 */
  className?: string;
}

// 样式化的标题文本
// 样式化的副标题文本
/**
 * M3E App Bar 组件
 * 
 * 基于 Material Design 3 规范的应用栏组件，提供页面标题和操作功能。
 * 
 * @example
 * ```tsx
 * <M3EAppBar
 *   title="页面标题"
 *   subtitle="副标题"
 *   navigationIcon={<Icon name="arrow-back" />}
 *   onNavigationPress={() => navigation.goBack()}
 *   actions={[
 *     <IconButton key="search" icon="search" />,
 *     <IconButton key="more" icon="more-vert" />
 *   ]}
 *   avatar={{
 *     source: { uri: 'https://example.com/avatar.jpg' },
 *     onPress: () => console.log('Avatar pressed')
 *   }}
 *   size="small"
 *   variant="default"
 *   elevated={true}
 * />
 * ```
 */
export const M3EAppBar: React.FC<M3EAppBarProps> = ({
  title,
  subtitle,
  navigationIcon,
  onNavigationPress,
  actions = [],
  avatar,
  thumbnail,
  size = 'small',
  titleAlignment = 'left',
  variant = 'default',
  elevated = false,
  searchConfig,
  className = '',
}) => {
  const baseClasses = 'bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700';
  const combinedClasses = `${baseClasses} ${className}`;

  // 渲染头像
  const renderAvatar = () => {
    if (!avatar) return null;

    return (
      <TouchableOpacity
        onPress={avatar.onPress}
        className="w-8 h-8 rounded-full overflow-hidden bg-purple-600 items-center justify-center"
        activeOpacity={0.7}
      >
        {avatar.source ? (
          <Image source={avatar.source} className="w-full h-full" resizeMode="cover" />
        ) : (
          <Text className="text-white text-sm font-medium">
            {avatar.monogram || 'A'}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  // 渲染搜索栏
  const renderSearchBar = () => {
    if (variant !== 'search' || !searchConfig) return null;

    return (
      <View className="flex-1 mx-4 bg-gray-100 dark:bg-gray-800 rounded-full px-4 py-2">
        <Text className="text-gray-600 dark:text-gray-400 text-base">
          {searchConfig.placeholder || 'Search'}
        </Text>
      </View>
    );
  };

  // 渲染标题内容
  const renderTitleContent = () => {
    if (variant === 'search') return null;

    const titleContainerClass = variant === 'centered' 
      ? 'flex-1 items-center justify-center'
      : 'flex-1 justify-center';

    const contentClass = titleAlignment === 'center' 
      ? 'items-center' 
      : 'items-start';

    return (
      <View className={`${titleContainerClass} px-4`}>
        <View className={contentClass}>
          {title && (
            <StyledTitleText
              size={size}
              alignment={titleAlignment}
              className="text-gray-900 dark:text-white"
            >
              {title}
            </StyledTitleText>
          )}
          {subtitle && (
            <StyledSubtitleText
              size={size}
              alignment={titleAlignment}
              className="text-gray-600 dark:text-gray-400 mt-1"
            >
              {subtitle}
            </StyledSubtitleText>
          )}
        </View>
      </View>
    );
  };

  // 渲染缩略图
  const renderThumbnail = () => {
    if (!thumbnail || variant !== 'image') return null;

    return (
      <View className="absolute left-14 right-12 top-3 bottom-3">
        <Image source={thumbnail} className="w-full h-full rounded" resizeMode="cover" />
      </View>
    );
  };

  return (
    <StyledAppBarContainer
      size={size}
      elevated={elevated}
      className={combinedClasses}
    >
      <View className="flex-row items-center h-full px-1">
        {/* 左侧导航图标 */}
        {navigationIcon && (
          <TouchableOpacity
            onPress={onNavigationPress}
            className="w-12 h-12 items-center justify-center"
            activeOpacity={0.7}
          >
            {navigationIcon}
          </TouchableOpacity>
        )}

        {/* 搜索栏 */}
        {renderSearchBar()}

        {/* 标题内容 */}
        {renderTitleContent()}

        {/* 缩略图 */}
        {renderThumbnail()}

        {/* 右侧操作区域 */}
        <View className="flex-row items-center">
          {/* 操作按钮 */}
          {actions.map((action, index) => (
            <View key={index} className="w-12 h-12 items-center justify-center">
              {action}
            </View>
          ))}

          {/* 头像 */}
          {avatar && (
            <View className="ml-2 mr-2">
              {renderAvatar()}
            </View>
          )}
        </View>
      </View>
    </StyledAppBarContainer>
  );
};

export default M3EAppBar;
