import React from 'react';
import { Text } from '@/components/base';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { Button, ButtonText, ButtonSpinner } from '@/components/ui/m3e-button';
import { Textarea, TextareaInput  } from '@/components/base';

interface ContinuationInputProps {
  content: string;
  onContentChange: (text: string) => void;
  onSubmit: () => void;
  isSubmitting: boolean;
}

export default function ContinuationInput({
  content,
  onContentChange,
  onSubmit,
  isSubmitting,
}: ContinuationInputProps) {
  const { t } = useTranslation();

  return (
    <View className="mt-4 pt-4 border-t border-outline-200 dark:border-outline-700">
      <Textarea size="md" h={80}>
        <TextareaInput
          value={content}
          onChangeText={onContentChange}
          placeholder={t(
            'story.writeContinuationPlaceholder',
            'Write your continuation here...'
          )}
          className="bg-background-50 dark:bg-background-700 text-typography-900 dark:text-typography-100 p-3 rounded-md border border-outline-200 dark:border-outline-700 mb-2"
          multiline
        />
      </Textarea>

      <Button
        action="primary"
        variant="solid"
        size="sm"
        onPress={onSubmit}
        isDisabled={isSubmitting}
        className="self-end px-4 py-2 rounded-md bg-primary-500 dark:bg-primary-600"
      >
        {isSubmitting ? (
          <ButtonSpinner color="$background" />
        ) : (
          <ButtonText className="text-typography-950 dark:text-typography-50">
            {t('story.submitContinuation', 'Submit')}
          </ButtonText>
        )}
      </Button>
    </View>
  );
}
