import React from 'react';
import { View } from 'react-native';

import { Text  } from '@/components/base';
import { Heading  } from '@/components/base';
import { useTranslation } from 'react-i18next';

export default function CreateStoryHeader() {
  const { t } = useTranslation();

  return (
    <View className="mb-6">
      <Heading size="xl" className="font-bold text-typography-900 mb-2">
        {t('createStoryTitle', '创建新故事')}
      </Heading>
      <Text size="md" className="text-typography-500">
        {t('storyForm.createDescription', '开始你的创作之旅，写下你的故事')}
      </Text>
    </View>
  );
}
