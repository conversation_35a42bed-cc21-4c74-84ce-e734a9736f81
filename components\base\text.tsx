/**
 * 基础文本组件
 * 替代 Gluestack UI 的 Text 和 Heading 组件
 */

import React from 'react';
import { Text as RNText, TextProps as RNTextProps } from 'react-native';
import { cn, createVariants, textSizes } from '@/utils/nativewind-helpers';

// Text 组件替代
const textVariants = createVariants({
  base: 'text-typography-950 dark:text-typography-50',
  variants: {
    size: textSizes,
    isTruncated: {
      true: 'truncate',
    },
    bold: {
      true: 'font-bold',
    },
    underline: {
      true: 'underline',
    },
    strikeThrough: {
      true: 'line-through',
    },
    italic: {
      true: 'italic',
    },
    highlight: {
      true: 'bg-yellow-200 dark:bg-yellow-800',
    },
    sub: {
      true: 'text-xs align-sub',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

export interface TextProps extends RNTextProps {
  className?: string;
  size?: keyof typeof textSizes;
  isTruncated?: boolean;
  bold?: boolean;
  underline?: boolean;
  strikeThrough?: boolean;
  italic?: boolean;
  highlight?: boolean;
  sub?: boolean;
}

export const Text = React.forwardRef<
  React.ComponentRef<typeof RNText>,
  TextProps
>(function Text(
  {
    className,
    size,
    isTruncated,
    bold,
    underline,
    strikeThrough,
    italic,
    highlight,
    sub,
    ...props
  },
  ref
) {
  return (
    <RNText
      ref={ref}
      {...props}
      className={textVariants({
        size,
        isTruncated,
        bold,
        underline,
        strikeThrough,
        italic,
        highlight,
        sub,
        class: className,
      })}
    />
  );
});

Text.displayName = 'Text';

// Heading 组件替代
const headingVariants = createVariants({
  base: 'font-bold text-typography-950 dark:text-typography-50',
  variants: {
    size: {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl',
      '2xl': 'text-2xl',
      '3xl': 'text-3xl',
      '4xl': 'text-4xl',
      '5xl': 'text-5xl',
      '6xl': 'text-6xl',
    },
    isTruncated: {
      true: 'truncate',
    },
    underline: {
      true: 'underline',
    },
    strikeThrough: {
      true: 'line-through',
    },
    italic: {
      true: 'italic',
    },
  },
  defaultVariants: {
    size: 'lg',
  },
});

export interface HeadingProps extends RNTextProps {
  className?: string;
  size?:
    | 'xs'
    | 'sm'
    | 'md'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl';
  isTruncated?: boolean;
  underline?: boolean;
  strikeThrough?: boolean;
  italic?: boolean;
}

export const Heading = React.forwardRef<
  React.ComponentRef<typeof RNText>,
  HeadingProps
>(function Heading(
  { className, size, isTruncated, underline, strikeThrough, italic, ...props },
  ref
) {
  return (
    <RNText
      ref={ref}
      {...props}
      className={headingVariants({
        size,
        isTruncated,
        underline,
        strikeThrough,
        italic,
        class: className,
      })}
    />
  );
});

Heading.displayName = 'Heading';
