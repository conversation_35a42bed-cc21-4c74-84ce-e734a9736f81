import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Send } from 'lucide-react-native';
import StoryOptimizationBlock from '@/features/stories/components/story-optimization-block';
import { useStoryOptimization } from '@/features/stories/hooks/use-story-optimization';

import { Text  } from '@/components/base';
import {
  M3ETextField,
  M3ETextFieldField,
} from '@/components/ui/m3e-text-field';
import { Textarea, TextareaInput  } from '@/components/base';
import {
  Button,
  ButtonText,
  ButtonIcon,
  ButtonSpinner,
} from '@/components/ui/m3e-button';

interface SegmentFormProps {
  content: string;
  setContent: (content: string) => void;
  title: string;
  setTitle: (title: string) => void;
  isSubmitting: boolean;
  error: string | null;
  isAIGenerated: boolean;
  setIsAIGenerated: (isAIGenerated: boolean) => void;
  onSubmit: () => void;
  onCancel: () => void;
}

export default function SegmentForm({
  content,
  setContent,
  title,
  setTitle,
  isSubmitting,
  error,
  isAIGenerated,
  setIsAIGenerated,
  onSubmit,
  onCancel,
}: SegmentFormProps) {
  const { t } = useTranslation();

  // 添加 AI 优化功能
  const { isOptimizing, handleOptimizeContent } = useStoryOptimization({
    onOptimizedContent: (optimizedContent) => {
      setContent(optimizedContent);
      setIsAIGenerated(true);
    },
  });

  // 处理 AI 优化
  const handleOptimize = (contentToOptimize: string, type: any) => {
    handleOptimizeContent(contentToOptimize);
  };

  return (
    <>
      <View className="mb-4">
        <Text className="text-base font-medium text-typography-900 dark:text-typography-100 mb-2">
          {t('createSegment.titleLabel', 'Title (Optional)')}
        </Text>
        <M3ETextField
          value={title}
          onChangeText={setTitle}
          placeholder={t(
            'createSegment.titlePlaceholder',
            'Enter a title for your segment'
          )}
          className="bg-background-50 dark:bg-background-800 border-outline-200 dark:border-outline-700 text-typography-900 dark:text-typography-100"
        />
      </View>

      <View className="mb-4">
        <Text className="text-base font-medium text-typography-900 dark:text-typography-100 mb-2">
          {t('createSegment.contentLabel', 'Content')}
        </Text>
        <Textarea size="md" h={200}>
          <TextareaInput
            value={content}
            onChangeText={setContent}
            placeholder={t(
              'createSegment.contentPlaceholder',
              'Write your story segment here...'
            )}
            className="bg-background-50 dark:bg-background-800 border-outline-200 dark:border-outline-700 text-typography-900 dark:text-typography-100"
          />
        </Textarea>
      </View>

      {error && (
        <Text className="text-error-600 dark:text-error-400 text-sm mt-2">
          {error}
        </Text>
      )}

      {/* AI 优化按钮 */}
      <StoryOptimizationBlock
        onOptimizeContent={handleOptimize}
        isOptimizing={isOptimizing}
        disabled={isSubmitting}
      />

      <View className="flex-row justify-between mt-4">
        <Button
          className="flex-1 bg-background-50 dark:bg-background-800 border border-outline-200 dark:border-outline-700 rounded-lg p-4 mr-2"
          onPress={onCancel}
          disabled={isSubmitting}
          variant="outline"
        >
          <ButtonText className="text-typography-900 dark:text-typography-100 text-base font-medium">
            {t('common.cancel', '取消')}
          </ButtonText>
        </Button>

        <Button
          className="flex-1 bg-primary-500 dark:bg-primary-600 rounded-lg p-4 flex-row items-center justify-center"
          onPress={onSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <ButtonSpinner color="$background" />
          ) : (
            <>
              <ButtonIcon as={Send} size={18} color="$background" />
              <ButtonText className="text-background-50 dark:text-background-950 text-base font-medium ml-2">
                {t('common.submit', '提交')}
              </ButtonText>
            </>
          )}
        </Button>
      </View>
    </>
  );
}
