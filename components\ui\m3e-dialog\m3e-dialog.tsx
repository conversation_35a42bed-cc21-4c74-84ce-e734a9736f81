import React from 'react';
import { Text } from '@/components/base';
import { View, Text, TouchableOpacity, Modal, ScrollView } from 'react-native';

// Dialog Action 的属性接口
export interface M3EDialogActionProps {
  /** 按钮文本 */
  label: string;
  /** 按钮类型 */
  variant?: 'text' | 'filled' | 'outlined';
  /** 点击事件 */
  onPress?: () => void;
  /** 是否禁用 */
  disabled?: boolean;
}

// Dialog 的属性接口
export interface M3EDialogProps {
  /** 是否显示对话框 */
  visible: boolean;
  /** 对话框类型 */
  type?: 'basic' | 'list' | 'scrollable';
  /** 对话框标题 */
  title: string;
  /** 支持文本 */
  supportingText?: string;
  /** 图标组件 */
  icon?: React.ReactNode;
  /** 列表项（仅在 type='list' 或 'scrollable' 时使用） */
  listItems?: Array<{
    id: string;
    label: string;
    selected?: boolean;
    onPress?: () => void;
  }>;
  /** 主要操作按钮 */
  primaryAction?: M3EDialogActionProps;
  /** 次要操作按钮 */
  secondaryAction?: M3EDialogActionProps;
  /** 自定义内容 */
  children?: React.ReactNode;
  /** 关闭事件 */
  onClose?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

// 获取操作按钮样式
const getActionButtonClasses = (variant: string, disabled: boolean) => {
  const variantClasses = {
    text: 'px-4 py-2.5',
    filled: 'bg-purple-600 rounded-full px-4 py-2.5',
    outlined: 'border border-gray-300 dark:border-gray-600 rounded-full px-4 py-2.5',
  };
  
  const disabledClass = disabled ? 'opacity-50' : '';
  
  return `${variantClasses[variant as keyof typeof variantClasses] || variantClasses.text} ${disabledClass}`;
};

/**
 * Dialog Action 组件
 */
const DialogAction: React.FC<M3EDialogActionProps> = ({
  label,
  variant = 'text',
  onPress,
  disabled = false,
}) => {
  const textColorClass = variant === 'filled' 
    ? 'text-white' 
    : 'text-purple-600 dark:text-purple-400';

  return (
    <TouchableOpacity
      onPress={disabled ? undefined : onPress}
      activeOpacity={0.7}
      className={`h-12 items-center justify-center ${getActionButtonClasses(variant, disabled)}`}
    >
      <Text className={`text-sm font-medium ${textColorClass}`}>{label}</Text>
    </TouchableOpacity>
  );
};

/**
 * Dialog List Item 组件
 */
const DialogListItem: React.FC<{
  label: string;
  selected?: boolean;
  onPress?: () => void;
}> = ({ label, selected = false, onPress }) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.7}
      className="flex-row items-center px-4 py-3 min-h-[56px]"
    >
      <View className="flex-row items-center flex-1 gap-4">
        <View className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 items-center justify-center">
          <Text className="text-base font-medium text-purple-600 dark:text-purple-400">
            {label.charAt(0).toUpperCase()}
          </Text>
        </View>
        
        <Text className="flex-1 text-base text-gray-900 dark:text-white">
          {label}
        </Text>
        
        {selected && (
          <View className="w-5 h-5 rounded-sm bg-purple-600 items-center justify-center">
            <Text className="text-xs text-white">✓</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

/**
 * M3E Dialog 组件
 *
 * 基于 Material Design 3 规范的对话框组件，提供重要提示和决策功能。
 *
 * @example
 * ```tsx
 * <M3EDialog
 *   visible={showDialog}
 *   type="basic"
 *   title="Basic dialog title"
 *   supportingText="A dialog is a type of modal window that appears in front of app content to provide critical information, or prompt for a decision to be made."
 *   primaryAction={{
 *     label: "Action 1",
 *     onPress: () => console.log('Primary action')
 *   }}
 *   secondaryAction={{
 *     label: "Action 2",
 *     onPress: () => console.log('Secondary action')
 *   }}
 *   onClose={() => setShowDialog(false)}
 * />
 * ```
 */
export const M3EDialog: React.FC<M3EDialogProps> = ({
  visible,
  type = 'basic',
  title,
  supportingText,
  icon,
  listItems = [],
  primaryAction,
  secondaryAction,
  children,
  onClose,
  className = '',
}) => {
  const baseClasses = 'bg-white dark:bg-gray-800 rounded-3xl shadow-lg max-w-sm mx-auto';
  const combinedClasses = `${baseClasses} ${className}`;

  const renderContent = () => {
    switch (type) {
      case 'list':
      case 'scrollable':
        return (
          <View className="flex-1">
            {/* Header */}
            <View className="px-6 pt-6 pb-4">
              <Text className="text-2xl font-normal text-gray-900 dark:text-white">
                {title}
              </Text>
              {supportingText && (
                <Text className="text-sm text-gray-600 dark:text-gray-400 mt-4 leading-5">
                  {supportingText}
                </Text>
              )}
            </View>

            {/* List Content */}
            <View className={type === 'scrollable' ? 'max-h-44' : ''}>
              <ScrollView 
                showsVerticalScrollIndicator={type === 'scrollable'}
                className="flex-1"
              >
                {listItems.map((item) => (
                  <DialogListItem
                    key={item.id}
                    label={item.label}
                    selected={item.selected}
                    onPress={item.onPress}
                  />
                ))}
              </ScrollView>
            </View>
          </View>
        );

      default: // basic
        return (
          <View className="px-6 pt-6">
            {icon && (
              <View className="items-center mb-4">
                <View className="w-6 h-6">
                  {icon}
                </View>
              </View>
            )}
            
            <Text className={`text-2xl font-normal text-gray-900 dark:text-white ${icon ? 'text-center' : ''}`}>
              {title}
            </Text>
            
            {supportingText && (
              <Text className={`text-sm text-gray-600 dark:text-gray-400 mt-4 leading-5 ${icon ? 'text-center' : ''}`}>
                {supportingText}
              </Text>
            )}
            
            {children}
          </View>
        );
    }
  };

  return (
    <Modal visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black/50 items-center justify-center p-6">
        <View className={combinedClasses}>
          {renderContent()}
          
          {/* Actions */}
          {(primaryAction || secondaryAction) && (
            <View className="flex-row justify-end items-center gap-2 px-6 pb-5 pt-5">
              {secondaryAction && <DialogAction {...secondaryAction} />}
              {primaryAction && <DialogAction {...primaryAction} />}
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default M3EDialog;
