import React, { useState } from 'react';
import { Alert , View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { ThemeOptionsGroup } from '../components/theme-options-group';
import { LanguageOptionsGroup } from '../components/language-options-group';
import { signOut as supabaseSignOut } from '@/api/auth';
import { useAuthStore } from '@/lib/store/auth-store';
import M3EDemoModal from '@/components/modals/m3e-demo-modal';


import { Text  } from '@/components/base';
import { Heading  } from '@/components/base';
import { ScrollView } from '@/components/ui/scroll-view';
import { Button, ButtonText } from '@/components/ui/m3e-button';

export default function SettingsScreen() {
  const { t } = useTranslation(['common', 'settings']);
  const clearAuthStore = useAuthStore((state) => state.signOut);
  const [showM3EDemo, setShowM3EDemo] = useState(false);

  const handleLogout = async () => {
    clearAuthStore();
    const { error } = await supabaseSignOut();
    if (error) {
      console.error('Error signing out from Supabase:', error);
      Alert.alert(
        t('logoutErrorTitle', 'Logout Error'),
        error.message ||
          t('logoutErrorGeneric', 'Failed to sign out. Please try again.')
      );
    }
  };

  return (
    <View className="flex-1 bg-background-50 dark:bg-background-950">
      {/* Content */}
      <ScrollView className="flex-1">
        {/* Theme Settings Section */}
        <View className="px-4 mt-6">
          <Text className="text-typography-900 dark:text-typography-50 text-lg font-medium mb-3">
            {t('themeTitle')}
          </Text>
          <View className="bg-background-100 dark:bg-background-900 rounded-md overflow-hidden">
            <ThemeOptionsGroup />
          </View>
        </View>

        {/* Language Settings Section */}
        <View className="px-4 mt-6">
          <Text className="text-typography-900 dark:text-typography-50 text-lg font-medium mb-3">
            {t('languageTitle')}
          </Text>
          <View className="bg-background-100 dark:bg-background-900 rounded-md overflow-hidden">
            <LanguageOptionsGroup />
          </View>
        </View>

        {/* Developer Section */}
        <View className="px-4 mt-6">
          <Text className="text-typography-900 dark:text-typography-50 text-lg font-medium mb-3">
            开发者选项
          </Text>
          <Button
            variant="outlined"
            onPress={() => setShowM3EDemo(true)}
            className="w-full py-3 mb-3"
          >
            <ButtonText className="font-medium">M3E 组件演示</ButtonText>
          </Button>
        </View>

        {/* Account Section */}
        <View className="px-4 mt-6 mb-8">
          <Text className="text-typography-900 dark:text-typography-50 text-lg font-medium mb-3">
            {t('accountTitle')}
          </Text>
          <Button
            action="negative"
            variant="solid"
            onPress={handleLogout}
            className="w-full bg-error-500 py-3"
          >
            <ButtonText className="font-medium">{t('logOut')}</ButtonText>
          </Button>
        </View>
      </ScrollView>

      {/* M3E Demo Modal */}
      <M3EDemoModal
        visible={showM3EDemo}
        onClose={() => setShowM3EDemo(false)}
      />
    </View>
  );
}
