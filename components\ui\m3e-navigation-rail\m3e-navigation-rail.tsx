import React from 'react';
import { Text } from '@/components/base';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { M3EBadge } from '../m3e-badge';

// Navigation Rail Item 的属性接口
export interface M3ENavigationRailItemProps {
  /** 图标组件 */
  icon: React.ReactNode;
  /** 选中状态的图标组件 */
  selectedIcon?: React.ReactNode;
  /** 标签文本 */
  label?: string;
  /** 是否选中 */
  selected?: boolean;
  /** 是否显示标签 */
  showLabel?: boolean;
  /** 徽章配置 */
  badge?: {
    label?: string | number;
    size?: 'small' | 'large';
    visible?: boolean;
  };
  /** 点击事件 */
  onPress?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

// Navigation Rail 的属性接口
export interface M3ENavigationRailProps {
  /** 导航项列表 */
  items: M3ENavigationRailItemProps[];
  /** 当前选中的索引 */
  selectedIndex?: number;
  /** 是否显示标签 */
  showLabels?: boolean;
  /** 导航栏类型 */
  type?: 'docked' | 'floating';
  /** 对齐方式 */
  alignment?: 'top' | 'middle';
  /** 浮动操作按钮 */
  fab?: React.ReactNode;
  /** 自定义样式类名 */
  className?: string;
  /** 选择变化事件 */
  onSelectionChange?: (index: number) => void;
}

// 获取容器样式
const getRailContainerClasses = (type: string, alignment: string) => {
  const typeClasses = {
    docked: 'bg-white dark:bg-gray-900',
    floating: 'bg-gray-100 dark:bg-gray-800 rounded-2xl mx-3 my-3',
  };

  const alignmentClasses = {
    top: 'justify-start',
    middle: 'justify-center',
  };

  return `${
    typeClasses[type as keyof typeof typeClasses] || typeClasses.docked
  } ${
    alignmentClasses[alignment as keyof typeof alignmentClasses] ||
    alignmentClasses.top
  }`;
};

// 获取图标容器样式
const getIconContainerClasses = (selected: boolean) => {
  return selected
    ? 'bg-purple-100 dark:bg-purple-900 rounded-2xl'
    : 'bg-transparent';
};

/**
 * M3E Navigation Rail Item 组件
 */
export const M3ENavigationRailItem: React.FC<M3ENavigationRailItemProps> = ({
  icon,
  selectedIcon,
  label,
  selected = false,
  showLabel = true,
  badge,
  onPress,
  className = '',
}) => {
  const displayIcon = selected && selectedIcon ? selectedIcon : icon;

  return (
    <TouchableOpacity
      onPress={onPress}
      className={`flex-col items-center gap-1 py-1.5 px-0 ${className}`}
      activeOpacity={0.7}
    >
      <View className="relative">
        <View
          className={`w-14 h-8 items-center justify-center relative ${getIconContainerClasses(
            selected
          )}`}
        >
          {displayIcon}
          {badge && badge.visible && (
            <M3EBadge
              label={badge.label}
              size={badge.size}
              className="absolute -top-1 -right-1"
            />
          )}
        </View>
      </View>

      {showLabel && label && (
        <Text
          className={`text-xs font-medium text-center ${
            selected
              ? 'text-gray-900 dark:text-white'
              : 'text-gray-600 dark:text-gray-400'
          }`}
        >
          {label}
        </Text>
      )}
    </TouchableOpacity>
  );
};

/**
 * M3E Navigation Rail 组件
 *
 * 基于 Material Design 3 规范的导航栏组件，提供侧边导航功能。
 *
 * @example
 * ```tsx
 * const navigationItems = [
 *   {
 *     icon: <Icon name="home" />,
 *     selectedIcon: <Icon name="home-filled" />,
 *     label: "首页",
 *     badge: { label: "3", visible: true }
 *   },
 *   {
 *     icon: <Icon name="search" />,
 *     label: "搜索"
 *   }
 * ];
 *
 * <M3ENavigationRail
 *   items={navigationItems}
 *   selectedIndex={0}
 *   onSelectionChange={(index) => console.log('Selected:', index)}
 * />
 * ```
 */
export const M3ENavigationRail: React.FC<M3ENavigationRailProps> = ({
  items,
  selectedIndex = 0,
  showLabels = true,
  type = 'docked',
  alignment = 'top',
  fab,
  className = '',
  onSelectionChange,
}) => {
  const baseClasses = 'w-24 min-h-full';
  const combinedClasses = `${baseClasses} ${className}`;

  return (
    <View
      className={`${combinedClasses} ${getRailContainerClasses(
        type,
        alignment
      )}`}
    >
      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
      >
        <View
          className={`flex-1 ${alignment === 'top' ? 'pt-11' : 'py-11'} px-0`}
        >
          {fab && <View className="items-center mb-8">{fab}</View>}

          <View className="flex-1 gap-1">
            {items.map((item, index) => (
              <M3ENavigationRailItem
                key={index}
                {...item}
                selected={index === selectedIndex}
                showLabel={showLabels}
                onPress={() => {
                  item.onPress?.();
                  onSelectionChange?.(index);
                }}
              />
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default M3ENavigationRail;
