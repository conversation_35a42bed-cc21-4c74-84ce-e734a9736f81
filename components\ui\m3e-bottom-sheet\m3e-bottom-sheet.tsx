import React, { useRef, useEffect } from 'react';
import { Text } from '@/components/base';
import { View, Text, Modal, TouchableOpacity, Animated, PanGestureHandler, Dimensions } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

// Bottom Sheet 的属性接口
export interface M3EBottomSheetProps {
  /** 是否显示 */
  visible: boolean;
  /** 是否为模态模式 */
  modal?: boolean;
  /** 是否显示拖拽手柄 */
  showDragHandle?: boolean;
  /** 标题 */
  title?: string;
  /** 子组件 */
  children?: React.ReactNode;
  /** 初始高度（百分比，0-1） */
  initialHeight?: number;
  /** 最小高度（百分比，0-1） */
  minHeight?: number;
  /** 最大高度（百分比，0-1） */
  maxHeight?: number;
  /** 是否可拖拽调整高度 */
  draggable?: boolean;
  /** 关闭事件 */
  onClose?: () => void;
  /** 高度变化事件 */
  onHeightChange?: (height: number) => void;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * M3E Bottom Sheet 组件
 *
 * 基于 Material Design 3 规范的底部表单组件，包含补充内容，锚定在屏幕底部。
 *
 * @example
 * ```tsx
 * <M3EBottomSheet
 *   visible={showBottomSheet}
 *   modal={true}
 *   showDragHandle={true}
 *   title="Bottom Sheet Title"
 *   initialHeight={0.5}
 *   draggable={true}
 *   onClose={() => setShowBottomSheet(false)}
 * >
 *   <View className="p-4">
 *     <Text>Bottom sheet content goes here</Text>
 *   </View>
 * </M3EBottomSheet>
 * ```
 */
export const M3EBottomSheet: React.FC<M3EBottomSheetProps> = ({
  visible,
  modal = true,
  showDragHandle = true,
  title,
  children,
  initialHeight = 0.5,
  minHeight = 0.2,
  maxHeight = 0.9,
  draggable = true,
  onClose,
  onHeightChange,
  className = '',
}) => {
  const translateY = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const currentHeight = useRef(initialHeight);
  const gestureRef = useRef<any>(null);

  useEffect(() => {
    if (visible) {
      // 显示动画
      Animated.spring(translateY, {
        toValue: SCREEN_HEIGHT * (1 - initialHeight),
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      // 隐藏动画
      Animated.spring(translateY, {
        toValue: SCREEN_HEIGHT,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [visible, initialHeight, translateY]);

  const handleGestureEvent = Animated.event(
    [{ nativeEvent: { translationY: translateY } }],
    { useNativeDriver: true }
  );

  const handleGestureEnd = (event: any) => {
    const { translationY, velocityY } = event.nativeEvent;
    const currentTranslateY = SCREEN_HEIGHT * (1 - currentHeight.current) + translationY;
    
    // 计算新的高度百分比
    let newHeightPercent = 1 - (currentTranslateY / SCREEN_HEIGHT);
    
    // 根据速度判断用户意图
    if (velocityY > 500) {
      // 快速向下滑动，关闭
      if (onClose) {
        onClose();
      }
      return;
    } else if (velocityY < -500) {
      // 快速向上滑动，展开到最大
      newHeightPercent = maxHeight;
    }
    
    // 限制在最小和最大高度之间
    newHeightPercent = Math.max(minHeight, Math.min(maxHeight, newHeightPercent));
    
    // 如果高度太小，关闭
    if (newHeightPercent < minHeight + 0.1) {
      if (onClose) {
        onClose();
      }
      return;
    }
    
    currentHeight.current = newHeightPercent;
    
    // 动画到新位置
    Animated.spring(translateY, {
      toValue: SCREEN_HEIGHT * (1 - newHeightPercent),
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
    
    // 通知高度变化
    if (onHeightChange) {
      onHeightChange(newHeightPercent);
    }
  };

  const handleBackdropPress = () => {
    if (modal && onClose) {
      onClose();
    }
  };

  const getContainerClasses = () => {
    let classes = 'bg-purple-50 dark:bg-purple-900 rounded-t-3xl';
    
    if (modal) {
      classes += ' shadow-lg';
    } else {
      classes += ' shadow-md';
    }
    
    return `${classes} ${className}`;
  };

  const renderContent = () => (
    <GestureHandlerRootView className="flex-1">
      <Animated.View
        style={{
          transform: [{ translateY }],
          height: SCREEN_HEIGHT * maxHeight,
        }}
        className={getContainerClasses()}
      >
        {/* Header with Drag Handle */}
        <View className="items-center pt-4 pb-2">
          {showDragHandle && (
            <PanGestureHandler
              ref={gestureRef}
              onGestureEvent={draggable ? handleGestureEvent : undefined}
              onHandlerStateChange={draggable ? handleGestureEnd : undefined}
              enabled={draggable}
            >
              <Animated.View>
                <View className="w-8 h-1 bg-gray-400 dark:bg-gray-600 rounded-full mb-3" />
              </Animated.View>
            </PanGestureHandler>
          )}
          
          {title && (
            <Text className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {title}
            </Text>
          )}
        </View>
        
        {/* Content */}
        <View className="flex-1">
          {children}
        </View>
      </Animated.View>
    </GestureHandlerRootView>
  );

  if (!visible) {
    return null;
  }

  if (modal) {
    return (
      <Modal visible={visible}
        transparent
        animationType="none"
        onRequestClose={onClose}
      >
        <View className="flex-1">
          {/* Backdrop/Scrim */}
          <TouchableOpacity
            className="flex-1 bg-black opacity-30"
            activeOpacity={1}
            onPress={handleBackdropPress}
          />
          
          {/* Bottom Sheet */}
          <View className="absolute bottom-0 left-0 right-0">
            {renderContent()}
          </View>
        </View>
      </Modal>
    );
  }

  return (
    <View className="absolute bottom-0 left-0 right-0 z-50">
      {renderContent()}
    </View>
  );
};

export default M3EBottomSheet;
