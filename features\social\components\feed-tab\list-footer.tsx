import React from 'react';
import { View } from 'react-native';

import { Text  } from '@/components/base';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

interface ListFooterProps {
  isLoading: boolean;
}

export function ListFooter({ isLoading }: ListFooterProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  if (!isLoading) return null;

  return (
    <View className="flex flex-row justify-center items-center py-4 space-x-2">
      <M3EProgressIndicator size="small" color={isDark ? 'primary.400' : 'primary.500'} />
      <Text
        className={`text-sm ${
          isDark ? 'text-typography-400' : 'text-typography-500'
        }`}
      >
        {t('social.feed.loadingMore', '加载更多...')}
      </Text>
    </View>
  );
}
