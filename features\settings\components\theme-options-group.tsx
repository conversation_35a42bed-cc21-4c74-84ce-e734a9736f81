import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useSettingsStore, ThemeMode } from '@/lib/store/settings-store';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

import { Pressable } from '@/components/base';
import { M3EText } from '@/components/ui/m3e-text';

export function ThemeOptionsGroup() {
  const { t } = useTranslation('settings');
  const themeMode = useSettingsStore((state) => state.themeMode);
  const setThemeMode = useSettingsStore((state) => state.setThemeMode);
  const { colors, isDark, setTheme, setIsSystemTheme } = useUnifiedTheme();

  // Define theme options
  const themeOptions: { value: ThemeMode; label: string }[] = [
    { value: 'light', label: t('lightMode') },
    { value: 'dark', label: t('darkMode') },
    { value: 'system', label: t('systemMode') },
  ];

  const handleThemeChange = (newTheme: ThemeMode) => {
    console.log('Theme change requested:', newTheme);
    setThemeMode(newTheme);

    // 同步更新统一主题系统
    if (newTheme === 'system') {
      setIsSystemTheme(true);
    } else {
      setTheme(newTheme as 'light' | 'dark');
    }
  };

  return (
    <View style={{ width: '100%', flexDirection: 'row' }}>
      {themeOptions.map((option) => (
        <Pressable
          key={option.value}
          onPress={() => handleThemeChange(option.value)}
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 16,
            backgroundColor:
              themeMode === option.value
                ? colors.primary
                : colors.surfaceContainer,
          }}
        >
          <M3EText
            variant="labelLarge"
            color={
              themeMode === option.value ? colors.onPrimary : colors.onSurface
            }
          >
            {option.label}
          </M3EText>
        </Pressable>
      ))}
    </View>
  );
}
