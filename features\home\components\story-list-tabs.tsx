import React from 'react';
import { View } from 'react-native';

import { Text  } from '@/components/base';
import { Pressable } from '@/components/base';


interface StoryListTabsProps {
  tabs: string[];
  activeTab: string;
  onTabPress: (tab: string) => void;
}

export function StoryListTabs({
  tabs,
  activeTab,
  onTabPress,
}: StoryListTabsProps) {
  return (
    <View className="flex flex-row mt-6 mb-4 border-b border-outline-200 dark:border-outline-700">
      {tabs.map((tab) => {
        const isActive = activeTab === tab;

        return (
          <Pressable key={tab}
            className={`mr-6 pb-2 ${
              isActive
                ? 'border-b-2 border-primary-500'
                : 'border-b-2 border-transparent'
            }`}
            onPress={() => onTabPress(tab)}
          >
            <Text
              className={`text-base font-medium ${
                isActive
                  ? 'text-primary-500'
                  : 'text-typography-500 dark:text-typography-400'
              }`}
            >
              {tab}
            </Text>
          </Pressable>
        );
      })}
    </View>
  );
}
