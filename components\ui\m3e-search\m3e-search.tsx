import React, { useState } from 'react';
import { Text } from '@/components/base';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  FlatList,
} from 'react-native';

// Search Result Item 的属性接口
export interface M3ESearchResultItem {
  /** 唯一标识符 */
  id: string;
  /** 主标题 */
  title: string;
  /** 副标题/描述 */
  subtitle?: string;
  /** 前导图标 */
  leadingIcon?: React.ReactNode;
  /** 头像 */
  avatar?: React.ReactNode;
  /** 点击事件 */
  onPress?: () => void;
}

// Search Bar 的属性接口
export interface M3ESearchBarProps {
  /** 搜索值 */
  value?: string;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否显示头像 */
  showAvatar?: boolean;
  /** 头像组件 */
  avatar?: React.ReactNode;
  /** 前导图标 */
  leadingIcon?: React.ReactNode;
  /** 尾部图标 */
  trailingIcon?: React.ReactNode;
  /** 是否禁用 */
  disabled?: boolean;
  /** 值变化事件 */
  onValueChange?: (value: string) => void;
  /** 搜索事件 */
  onSearch?: (value: string) => void;
  /** 清除事件 */
  onClear?: () => void;
  /** 点击事件 */
  onPress?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

// Search View 的属性接口
export interface M3ESearchViewProps {
  /** 是否显示搜索视图 */
  visible: boolean;
  /** 搜索值 */
  value?: string;
  /** 占位符文本 */
  placeholder?: string;
  /** 搜索结果列表 */
  results?: M3ESearchResultItem[];
  /** 是否显示结果列表 */
  showResults?: boolean;
  /** 是否全屏模式 */
  fullScreen?: boolean;
  /** 值变化事件 */
  onValueChange?: (value: string) => void;
  /** 搜索事件 */
  onSearch?: (value: string) => void;
  /** 关闭事件 */
  onClose?: () => void;
  /** 结果项点击事件 */
  onResultPress?: (item: M3ESearchResultItem) => void;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * M3E Search Bar 组件
 *
 * 基于 Material Design 3 规范的搜索栏组件。
 *
 * @example
 * ```tsx
 * <M3ESearchBar
 *   value={searchValue}
 *   placeholder="Search..."
 *   onValueChange={setSearchValue}
 *   onSearch={handleSearch}
 *   onPress={() => setShowSearchView(true)}
 * />
 * ```
 */
export const M3ESearchBar: React.FC<M3ESearchBarProps> = ({
  value = '',
  placeholder = 'Search...',
  showAvatar = false,
  avatar,
  leadingIcon,
  trailingIcon,
  disabled = false,
  onValueChange,
  onSearch,
  onClear,
  onPress,
  className = '',
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleSubmit = () => {
    if (onSearch) {
      onSearch(value);
    }
  };

  const handleClear = () => {
    if (onValueChange) {
      onValueChange('');
    }
    if (onClear) {
      onClear();
    }
  };

  const getContainerClasses = () => {
    // 基于Figma M3E设计的搜索栏样式
    let classes = 'flex-row items-center h-14 px-1 rounded-full';

    // 背景色 - 使用M3E surface container high
    classes += ' bg-surface-container-high dark:bg-surface-container-high-dark';

    if (disabled) {
      classes += ' opacity-40';
    }

    if (isFocused) {
      // 聚焦时的边框 - 使用primary色
      classes += ' border border-primary dark:border-primary-dark';
    } else {
      // 默认边框 - 使用outline variant
      classes +=
        ' border border-outline-variant dark:border-outline-variant-dark';
    }

    return `${classes} ${className}`;
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={onPress ? 0.7 : 1}
      className={getContainerClasses()}
      disabled={disabled}
    >
      {/* Leading Icon or Avatar */}
      {showAvatar && avatar ? (
        <View className="w-10 h-10 ml-2 mr-3 rounded-full overflow-hidden">
          {avatar}
        </View>
      ) : leadingIcon ? (
        <View className="w-10 h-10 items-center justify-center ml-2 mr-3">
          {leadingIcon}
        </View>
      ) : (
        <View className="w-10 h-10 items-center justify-center ml-2 mr-3">
          <Text className="text-on-surface-variant dark:text-on-surface-variant-dark text-lg">
            🔍
          </Text>
        </View>
      )}

      {/* Search Input */}
      <View className="flex-1">
        {onPress ? (
          <Text className="text-base text-on-surface-variant dark:text-on-surface-variant-dark">
            {value || placeholder}
          </Text>
        ) : (
          <TextInput
            value={value}
            placeholder={placeholder}
            placeholderTextColor="#79747E"
            onChangeText={onValueChange}
            onSubmitEditing={handleSubmit}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            editable={!disabled}
            className="text-base text-on-surface dark:text-on-surface-dark py-2"
          />
        )}
      </View>

      {/* Trailing Icons */}
      <View className="flex-row items-center mr-2">
        {value && !onPress && (
          <TouchableOpacity
            onPress={handleClear}
            className="w-10 h-10 items-center justify-center rounded-full"
            activeOpacity={0.7}
          >
            <View className="w-6 h-6 bg-on-surface-variant dark:bg-on-surface-variant-dark rounded-full items-center justify-center">
              <Text className="text-surface dark:text-surface-dark text-xs font-medium">
                ×
              </Text>
            </View>
          </TouchableOpacity>
        )}

        {trailingIcon && (
          <TouchableOpacity
            onPress={handleSubmit}
            className="w-10 h-10 items-center justify-center rounded-full"
            activeOpacity={0.7}
          >
            {trailingIcon}
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};

/**
 * M3E Search View 组件
 *
 * 全屏或模态搜索视图组件。
 *
 * @example
 * ```tsx
 * <M3ESearchView
 *   visible={showSearchView}
 *   value={searchValue}
 *   results={searchResults}
 *   showResults={true}
 *   onValueChange={setSearchValue}
 *   onSearch={handleSearch}
 *   onClose={() => setShowSearchView(false)}
 *   onResultPress={handleResultPress}
 * />
 * ```
 */
export const M3ESearchView: React.FC<M3ESearchViewProps> = ({
  visible,
  value = '',
  placeholder = 'Search...',
  results = [],
  showResults = false,
  fullScreen = true,
  onValueChange,
  onSearch,
  onClose,
  onResultPress,
  className = '',
}) => {
  const handleSubmit = () => {
    if (onSearch) {
      onSearch(value);
    }
  };

  const handleClear = () => {
    if (onValueChange) {
      onValueChange('');
    }
  };

  const renderResultItem = ({ item }: { item: M3ESearchResultItem }) => (
    <TouchableOpacity
      onPress={() => {
        item.onPress?.();
        onResultPress?.(item);
      }}
      className="flex-row items-center px-4 py-3 border-b border-gray-200 dark:border-gray-700"
    >
      {/* Leading Element */}
      {item.avatar ? (
        <View className="w-10 h-10 mr-4">{item.avatar}</View>
      ) : item.leadingIcon ? (
        <View className="w-6 h-6 mr-4">{item.leadingIcon}</View>
      ) : null}

      {/* Content */}
      <View className="flex-1">
        <Text className="text-base font-normal text-gray-900 dark:text-white">
          {item.title}
        </Text>
        {item.subtitle && (
          <Text className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {item.subtitle}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  if (!visible) {
    return null;
  }

  return (
    <Modal visible={visible}
      animationType={fullScreen ? 'slide' : 'fade'}
      presentationStyle={fullScreen ? 'fullScreen' : 'overFullScreen'}
      onRequestClose={onClose}
    >
      <View className={`flex-1 bg-white dark:bg-gray-900 ${className}`}>
        {/* Header */}
        <View className="bg-purple-50 dark:bg-purple-900 px-1 py-2 rounded-t-3xl">
          <View className="flex-row items-center h-14">
            {/* Back Button */}
            <TouchableOpacity
              onPress={onClose}
              className="w-12 h-12 items-center justify-center ml-1"
            >
              <Text className="text-gray-600 dark:text-gray-400 text-lg">
                ←
              </Text>
            </TouchableOpacity>

            {/* Search Input */}
            <View className="flex-1 mx-2">
              <TextInput
                value={value}
                placeholder={placeholder}
                placeholderTextColor="#9CA3AF"
                onChangeText={onValueChange}
                onSubmitEditing={handleSubmit}
                autoFocus
                className="text-base text-gray-900 dark:text-white py-2"
              />
            </View>

            {/* Clear/Search Button */}
            {value ? (
              <TouchableOpacity
                onPress={handleClear}
                className="w-12 h-12 items-center justify-center mr-1"
              >
                <View className="w-6 h-6 bg-gray-400 rounded-full items-center justify-center">
                  <Text className="text-white text-xs">×</Text>
                </View>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={handleSubmit}
                className="w-12 h-12 items-center justify-center mr-1"
              >
                <Text className="text-gray-600 dark:text-gray-400">🔍</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Divider */}
        <View className="h-px bg-gray-300 dark:bg-gray-600" />

        {/* Results */}
        {showResults && results.length > 0 ? (
          <FlatList
            data={results}
            renderItem={renderResultItem}
            keyExtractor={(item) => item.id}
            className="flex-1"
          />
        ) : (
          <View className="flex-1" />
        )}
      </View>
    </Modal>
  );
};

export default M3ESearchBar;
