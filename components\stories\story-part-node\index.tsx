import React, { useState } from 'react';
import { Alert , View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/lib/store/auth-store';
import ActionButtons from './action-buttons';
import ContinuationInput from './continuation-input';

import { Text  } from '@/components/base';

// Types
export interface StoryPart {
  id: string;
  content: string;
  authorId: string;
  authorName: string;
  isAiGenerated?: boolean;
  createdAt: string;
  likes?: number;
  parentId?: string | null;
  children?: StoryPart[];
}

interface StoryPartNodeProps {
  part: StoryPart;
  level?: number;
  onAddContinuation?: (
    parentId: string,
    content: string,
    isAiGenerated?: boolean
  ) => Promise<void>;
}

export default function StoryPartNode({
  part,
  level = 0,
  onAddContinuation,
}: StoryPartNodeProps) {
  const { t } = useTranslation();
  const user = useAuthStore((state) => state.user);

  const [showContinueInput, setShowContinueInput] = useState(false);
  const [continuationContent, setContinuationContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleToggleContinueInput = () => {
    setShowContinueInput(!showContinueInput);
    setContinuationContent('');
  };

  const handleSubmitContinuation = async () => {
    if (!continuationContent.trim()) {
      Alert.alert(
        t('error', 'Error'),
        t('story.emptyContinuation', 'Please write something to continue the story.')
      );
      return;
    }

    if (!user) {
      Alert.alert(
        t('error', 'Error'),
        t('auth.loginRequired', 'You need to be logged in to continue the story.')
      );
      return;
    }

    setIsSubmitting(true);

    try {
      if (onAddContinuation) {
        await onAddContinuation(part.id, continuationContent.trim());
      }
      setContinuationContent('');
      setShowContinueInput(false);
    } catch (error) {
      console.error('Failed to add continuation:', error);
      Alert.alert(
        t('error', 'Error'),
        t('story.failedToAddContinuation', 'Failed to add your continuation. Please try again.')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View className={level > 0 ? "ml-2 border-l border-outline-200 dark:border-outline-700 pl-2 mt-2" : ""}
    >
      <View className="mb-4 p-4 bg-background-100 dark:bg-background-800 rounded-lg border border-outline-200 dark:border-outline-700">
        <Text className="text-base text-typography-900 dark:text-typography-100 leading-6 mb-2">
          {part.content}
        </Text>
        <Text className="text-sm text-typography-500 dark:text-typography-400 text-right mb-4 font-medium">
          {part.isAiGenerated
            ? t('story.aiGenerated', 'AI Generated')
            : t('story.writtenBy', 'Written by {{author}}', {
                author: part.authorName,
              })}
        </Text>

        <ActionButtons
          isLoggedIn={!!user}
          showContinueInput={showContinueInput}
          onToggleContinueInput={handleToggleContinueInput}
        />

        {showContinueInput && (
          <ContinuationInput
            content={continuationContent}
            onContentChange={setContinuationContent}
            onSubmit={handleSubmitContinuation}
            isSubmitting={isSubmitting}
          />
        )}
      </View>

      {part.children && part.children.length > 0 && (
        <View className="mt-1">
          {part.children.map((childPart) => (
            <StoryPartNode
              key={childPart.id}
              part={childPart}
              level={level + 1}
              onAddContinuation={onAddContinuation}
            />
          ))}
        </View>
      )}
    </View>
  );
}
