import React, { useState, useEffect } from 'react';
import { View, ScrollView, ActivityIndicator } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { useTranslation } from 'react-i18next';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

import { StoryWithSegments, StorySegment } from '@/api/stories';
import { Text  } from '@/components/base';

import { M3EButtonOutlined } from '@/components/ui/m3e-button';

// Components
import StoryDetailHeader from './story-detail-header';
import StorySegmentItem from './story-segment-item';
import AddSegmentForm from './add-segment-form';
import AISuggestionBlock from './aisuggestion-block';
import BranchManager from './branch-manager';

interface StoryDetailContentProps {
  story: StoryWithSegments;
  segments: StorySegment[];
  isLiked: boolean;
  likeCount: number;
  isLiking: boolean;
  newSegmentContent: string;
  isSubmittingSegment: boolean;
  showAISegmentSuggestions: boolean;
  loadingAISegmentSuggestions: boolean;
  aiSegmentSuggestions: string[];
  currentSegmentId?: string;
  hasMoreSegments: boolean;
  isLoadingMore: boolean;
  onLoadMoreSegments: () => void;
  onLikeToggle: () => void;
  onContentChange: (text: string) => void;
  onSubmitSegment: () => void;
  onFetchAISuggestions: () => void;
  onSelectAISuggestion: (suggestion: string) => void;
  onBranchChange?: (segmentId: string) => void;
}

export function StoryDetailContent({
  story,
  segments,
  isLiked,
  likeCount,
  isLiking,
  newSegmentContent,
  isSubmittingSegment,
  showAISegmentSuggestions,
  loadingAISegmentSuggestions,
  aiSegmentSuggestions,
  currentSegmentId,
  hasMoreSegments,
  isLoadingMore,
  onLoadMoreSegments,
  onLikeToggle,
  onContentChange,
  onSubmitSegment,
  onFetchAISuggestions,
  onSelectAISuggestion,
  onBranchChange}: StoryDetailContentProps) {
  const { t } = useTranslation();
  const { colors } = useUnifiedTheme();

  // 获取AI建议
  const handleRequestAiSuggestion = async (): Promise<string | null> => {
    if (loadingAISegmentSuggestions) return null;

    onFetchAISuggestions();

    // 等待AI建议生成
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (!loadingAISegmentSuggestions && aiSegmentSuggestions.length > 0) {
          clearInterval(checkInterval);
          resolve(aiSegmentSuggestions[0]);
        } else if (!loadingAISegmentSuggestions) {
          clearInterval(checkInterval);
          resolve(null);
        }
      }, 500);
    });
  };

  return (
    <ScrollView
      className="flex-1"
      contentContainerStyle={{ padding: 16 }}
      keyboardShouldPersistTaps="handled"
    >
      <StoryDetailHeader
        story={story}
        isLiked={isLiked}
        likeCount={likeCount}
        onLikeToggle={onLikeToggle}
        isLiking={isLiking}
      />

      {/* 分支管理器 */}
      <View className="mb-4">
        <BranchManager
          storyId={story.id}
          initialSegmentId={currentSegmentId || segments[0]?.id}
          onBranchChange={onBranchChange}
          onRequestAiSuggestion={handleRequestAiSuggestion}
        />
      </View>

      {/* 调试信息 */}
      <View className="p-3 bg-gray-100 dark:bg-gray-800 my-3 rounded-lg">
        <Text className="font-bold text-gray-800 dark:text-gray-200">
          调试信息:
        </Text>
        <Text className="text-gray-700 dark:text-gray-300">
          当前段落ID: {currentSegmentId || '无'}
        </Text>
        <Text className="text-gray-700 dark:text-gray-300">
          段落数量: {segments.length}
        </Text>
        <Text className="text-gray-700 dark:text-gray-300">
          段落IDs: {segments.map((s) => s.id.substring(0, 6)).join(', ')}
        </Text>
        <Text className="text-gray-700 dark:text-gray-300">段落关系:</Text>
        {segments.map((segment, index) => (
          <Text
            key={segment.id}
            className="text-sm text-gray-600 dark:text-gray-400"
          >
            {index + 1}. ID: {segment.id.substring(0, 6)}, 父ID:{' '}
            {segment.parent_segment_id
              ? segment.parent_segment_id.substring(0, 6)
              : 'ROOT'}
            , 子数量:{' '}
            {typeof segment.children_count === 'number'
              ? segment.children_count
              : '未知'}
          </Text>
        ))}
      </View>

      <View className="mt-6">
        <Text
          size="lg"
          className="font-bold text-gray-900 dark:text-white mb-4"
        >
          {t('storyDetail.storyContent', 'Story Content')}
        </Text>
        {segments && segments.length > 0 ? (
          <View style={{ height: Math.min(segments.length * 200, 2000) }}>
            <FlashList
              data={segments}
              renderItem={({ item, index }) => (
                <StorySegmentItem
                  segment={item}
                  isFirst={index === 0}
                  isLast={index === segments.length - 1}
                  showDivider={index > 0}
                />
              )}
              keyExtractor={(item) => item.id.toString()}
              estimatedItemSize={200}
              scrollEnabled={true} // 允许滚动
              optimizeItemArrangement={true} // 优化项目排列
              drawDistance={400} // 提前渲染的距离
              onEndReached={hasMoreSegments ? onLoadMoreSegments : undefined}
              onEndReachedThreshold={0.5} // 当距离底部50%时触发加载更多
              ListFooterComponent={
                isLoadingMore ? (
                  <View style={{ padding: 20, alignItems: 'center' }}>
                    <ActivityIndicator
                      size="small"
                      color={colors.primary}
                    />
                    <Text className="mt-2 text-gray-600 dark:text-gray-400">
                      {t('storyDetail.loadingMore', 'Loading more...')}
                    </Text>
                  </View>
                ) : hasMoreSegments ? (
                  <View className="p-4 mx-2">
                    <M3EButtonOutlined
                      size="medium"
                      icon="expand_more"
                      onPress={onLoadMoreSegments}
                      fullWidth={true}
                    >
                      {t('storyDetail.loadMore', 'Load more')}
                    </M3EButtonOutlined>
                  </View>
                ) : null
              }
              overrideItemLayout={(layout, item) => {
                // 可以根据内容长度动态调整高度
                layout.size = Math.max(
                  150,
                  Math.min(300, item.content.length / 2)
                );
              }}
            />
          </View>
        ) : (
          <Text className="text-center text-gray-600 dark:text-gray-400 p-8 italic">
            {t(
              'storyDetail.noContent',
              'This story has no content yet. Be the first to add to it!'
            )}
          </Text>
        )}
      </View>

      <AddSegmentForm
        segmentContent={newSegmentContent}
        onContentChange={onContentChange}
        onSubmit={onSubmitSegment}
        isSubmitting={isSubmittingSegment}
        onCreateBranchClick={() => {
          // 当用户点击创建分支按钮时，显示BranchManager组件的创建分支表单
          // 这里可以通过ref或其他方式调用BranchManager组件的方法
          // 或者通过状态控制CreateBranchForm组件的显示
          if (currentSegmentId) {
            // 这里可以实现显示创建分支表单的逻辑
          }
        }}
      />

      <AISuggestionBlock
        onFetchSuggestions={onFetchAISuggestions}
        loadingSuggestions={loadingAISegmentSuggestions}
        showSuggestions={showAISegmentSuggestions}
        suggestions={aiSegmentSuggestions}
        onSelectSuggestion={onSelectAISuggestion}
        isProcessingSegment={isSubmittingSegment}
      />
    </ScrollView>
  );
}
