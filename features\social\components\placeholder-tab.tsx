import React, { ReactNode } from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { Text  } from '@/components/base';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';



interface PlaceholderTabProps {
  icon: ReactNode;
  title: string;
  subtitle: string;
  actionLabel?: string;
  onAction?: () => void;
  actionIcon?: ReactNode;
}

export function PlaceholderTab({
  icon,
  title,
  subtitle,
  actionLabel,
  onAction,
  actionIcon,
}: PlaceholderTabProps) {
  const { t } = useTranslation();

  return (
    <View className="flex justify-center items-center flex-1 px-8 bg-background-50 dark:bg-background-900">
      <View className="flex flex-col items-center">
        <View className="mb-6">
          {icon}
        </View>
        
        <Text className="text-xl font-bold text-typography-900 dark:text-typography-100 text-center mb-2">
          {title}
        </Text>
        
        <Text className="text-base text-typography-500 dark:text-typography-400 text-center mb-8">
          {subtitle}
        </Text>
        
        {actionLabel && onAction && (
          <Button
            size="lg"
            variant="solid"
            action="primary"
            onPress={onAction}
            className="px-6 py-3 rounded-lg bg-primary-500 dark:bg-primary-600 flex-row items-center"
          >
            {actionIcon && (
              <ButtonIcon className="mr-2">
                {actionIcon}
              </ButtonIcon>
            )}
            <ButtonText className="text-typography-950 dark:text-typography-50 font-bold">
              {actionLabel}
            </ButtonText>
          </Button>
        )}
      </View>
    </View>
  );
}
