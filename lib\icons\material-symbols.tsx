'use client';
import React from 'react';
import { Text } from '@/components/base';
import { View, Text, StyleSheet } from 'react-native';
import type { ViewStyle, TextStyle } from 'react-native';

// Material Symbols 图标类型定义
export type MaterialSymbolName =
  | 'add'
  | 'edit'
  | 'delete'
  | 'close'
  | 'check'
  | 'arrow_back'
  | 'arrow_forward'
  | 'expand_more'
  | 'expand_less'
  | 'menu'
  | 'search'
  | 'home'
  | 'person'
  | 'settings'
  | 'notifications'
  | 'favorite'
  | 'star'
  | 'share'
  | 'more_vert'
  | 'visibility'
  | 'visibility_off'
  | 'lock'
  | 'lock_open'
  | 'email'
  | 'phone'
  | 'location_on'
  | 'calendar_today'
  | 'schedule'
  | 'info'
  | 'warning'
  | 'error'
  | 'help'
  | 'refresh'
  | 'download'
  | 'upload'
  | 'file_copy'
  | 'folder'
  | 'image'
  | 'video_call'
  | 'camera'
  | 'mic'
  | 'volume_up'
  | 'volume_off'
  | 'play_arrow'
  | 'pause'
  | 'stop'
  | 'skip_next'
  | 'skip_previous'
  | 'bookmark'
  | 'bookmark_border'
  | 'thumb_up'
  | 'thumb_down'
  | 'comment'
  | 'chat'
  | 'send'
  | 'attach_file'
  | 'link'
  | 'launch'
  | 'open_in_new'
  | 'shopping_cart'
  | 'payment'
  | 'account_circle'
  | 'group'
  | 'public'
  | 'language'
  | 'trending_up'
  | 'analytics'
  | 'dashboard'
  | 'work'
  | 'school'
  | 'local_hospital'
  | 'restaurant'
  | 'hotel'
  | 'flight'
  | 'directions_car'
  | 'train'
  | 'pets'
  | 'park'
  | 'fitness_center'
  | 'sports'
  | 'music_note'
  | 'movie'
  | 'gamepad'
  | 'palette'
  | 'brush'
  | 'auto_stories'
  | 'library_books'
  | 'history_edu'
  | 'psychology'
  | 'science'
  | 'biotech'
  | 'eco'
  | 'energy_savings_leaf'
  | 'sunny'
  | 'cloudy'
  | 'rainy'
  | 'snowing'
  | 'thunderstorm'
  | 'grade'
  | 'workspace_premium'
  | 'verified'
  | 'security'
  | 'privacy_tip'
  | 'bug_report'
  | 'feedback'
  | 'support'
  | 'contact_support'
  | 'quiz'
  | 'dark_mode'
  | 'light_mode'
  | 'save'
  | 'block'
  | 'cancel'
  | 'cloud_sync'
  | 'add_circle';

// Material Symbols 图标映射 (Unicode字符)
const MATERIAL_SYMBOLS_MAP: Record<MaterialSymbolName, string> = {
  // 基础操作
  add: '\ue145',
  edit: '\ue3c9',
  delete: '\ue872',
  close: '\ue5cd',
  check: '\ue5ca',

  // 导航
  arrow_back: '\ue5c4',
  arrow_forward: '\ue5c8',
  expand_more: '\ue5cf',
  expand_less: '\ue5ce',
  menu: '\ue5d2',

  // 搜索和主页
  search: '\ue8b6',
  home: '\ue88a',

  // 用户和社交
  person: '\ue7fd',
  account_circle: '\ue853',
  group: '\ue7ef',

  // 设置和通知
  settings: '\ue8b8',
  notifications: '\ue7f4',

  // 交互
  favorite: '\ue87d',
  star: '\ue838',
  share: '\ue80d',
  more_vert: '\ue5d4',

  // 可见性
  visibility: '\ue8f4',
  visibility_off: '\ue8f5',

  // 安全
  lock: '\ue897',
  lock_open: '\ue898',

  // 联系
  email: '\ue0be',
  phone: '\ue0cd',
  location_on: '\ue0c8',

  // 时间
  calendar_today: '\ue935',
  schedule: '\ue8b5',

  // 状态
  info: '\ue88e',
  warning: '\ue002',
  error: '\ue000',
  help: '\ue887',

  // 操作
  refresh: '\ue5d5',
  download: '\ue2c4',
  upload: '\ue2c6',

  // 文件
  file_copy: '\ue173',
  folder: '\ue2c7',
  image: '\ue3f4',

  // 媒体
  video_call: '\ue0b0',
  camera: '\ue3af',
  mic: '\ue029',
  volume_up: '\ue050',
  volume_off: '\ue04f',

  // 播放控制
  play_arrow: '\ue037',
  pause: '\ue034',
  stop: '\ue047',
  skip_next: '\ue044',
  skip_previous: '\ue045',

  // 书签和收藏
  bookmark: '\ue866',
  bookmark_border: '\ue867',

  // 反馈交互
  thumb_up: '\ue8dc',
  thumb_down: '\ue8db',
  comment: '\ue0b9',
  chat: '\ue0bf',
  send: '\ue163',
  attach_file: '\ue226',
  link: '\ue157',
  launch: '\ue895',
  open_in_new: '\ue89e',

  // 商业
  shopping_cart: '\ue8cc',
  payment: '\ue8a1',

  // 公共
  public: '\ue80b',
  language: '\ue894',

  // 数据和分析
  trending_up: '\ue8e6',
  analytics: '\ue85b',
  dashboard: '\ue871',

  // 工作和学习
  work: '\ue8f9',
  school: '\ue80c',
  local_hospital: '\ue548',
  restaurant: '\ue56c',
  hotel: '\ue549',

  // 交通
  flight: '\ue539',
  directions_car: '\ue1b9',
  train: '\ue570',

  // 生活方式
  pets: '\ue91d',
  park: '\ue55f',
  fitness_center: '\ue547',
  sports: '\ue84f',

  // 媒体和艺术
  music_note: '\ue405',
  movie: '\ue02c',
  gamepad: '\ue30f',
  palette: '\ue40a',
  brush: '\ue3a6',

  // 教育和学习
  auto_stories: '\ue06b',
  library_books: '\ue02f',
  history_edu: '\ue94a',
  psychology: '\uf085',
  science: '\ue8cc',
  biotech: '\ue82d',

  // 环境
  eco: '\ue01f',
  energy_savings_leaf: '\uf839',

  // 天气
  sunny: '\ue81a',
  cloudy: '\ue80f',
  rainy: '\ue818',
  snowing: '\ue80a',
  thunderstorm: '\ue81e',

  // 等级和认证
  grade: '\ue885',
  workspace_premium: '\ue7af',
  verified: '\ue8e8',

  // 安全和隐私
  security: '\ue32a',
  privacy_tip: '\uf0dc',

  // 支持和反馈
  bug_report: '\ue825',
  feedback: '\ue87f',
  support: '\ue8fe',
  contact_support: '\ue94c',
  quiz: '\uf04a',

  // 新增的按钮图标
  dark_mode: '\ue51c',
  light_mode: '\ue518',
  save: '\ue161',
  block: '\ue14b',
  cancel: '\ue5c9',
  cloud_sync: '\ue2bd',
  add_circle: '\ue147',

  // 评价
  thumb_up: '\ue8dc',
  thumb_down: '\ue8db',

  // 沟通
  comment: '\ue0b9',
  chat: '\ue0b7',
  send: '\ue163',
  attach_file: '\ue226',

  // 链接
  link: '\ue157',
  launch: '\ue895',
  open_in_new: '\ue89e',

  // 商务
  shopping_cart: '\ue8cc',
  payment: '\ue8a1',

  // 公共和语言
  public: '\ue80b',
  language: '\ue894',

  // 分析
  trending_up: '\ue8e6',
  analytics: '\ue902',
  dashboard: '\ue871',

  // 场所
  work: '\ue8f9',
  school: '\ue80c',
  local_hospital: '\ue548',
  restaurant: '\ue56c',
  hotel: '\ue549',
  flight: '\ue539',
  directions_car: '\ue1b2',
  train: '\ue570',

  // 生活
  pets: '\ue91d',
  park: '\ue562',
  fitness_center: '\ue53e',
  sports: '\ue85e',

  // 娱乐
  music_note: '\ue405',
  movie: '\ue02c',
  gamepad: '\ue30f',
  palette: '\ue40a',
  brush: '\ue3a2',

  // 学习和故事
  auto_stories: '\ue619',
  library_books: '\ue02f',
  history_edu: '\ue9d0',
  psychology: '\ue9da',

  // 科学
  science: '\ue8cc',
  biotech: '\ue94c',
  eco: '\ue850',
  energy_savings_leaf: '\ue1ab',

  // 天气
  sunny: '\ue81a',
  cloudy: '\ue80f',
  rainy: '\ue811',
  snowing: '\ue80e',
  thunderstorm: '\ue81d',

  // 质量和认证
  grade: '\ue885',
  workspace_premium: '\ue99b',
  verified: '\ue8e8',

  // 安全和支持
  security: '\ue32a',
  privacy_tip: '\uf0dc',
  bug_report: '\ue868',
  feedback: '\ue87f',
  support: '\uf1c2',
  contact_support: '\ue94c',
  quiz: '\uf04a',
};

// Material Symbols 样式类型
export type MaterialSymbolStyle = 'outlined' | 'rounded' | 'sharp';
export type MaterialSymbolWeight = 100 | 200 | 300 | 400 | 500 | 600 | 700;
export type MaterialSymbolSize =
  | 'small'
  | 'medium'
  | 'large'
  | 'xlarge'
  | number;

// 组件属性接口
interface MaterialSymbolProps {
  name: MaterialSymbolName;
  size?: MaterialSymbolSize;
  color?: string;
  style?: ViewStyle;
  weight?: MaterialSymbolWeight;
  symbolStyle?: MaterialSymbolStyle;
  className?: string;
}

// 尺寸映射
const SIZE_MAP: Record<Exclude<MaterialSymbolSize, number>, number> = {
  small: 16,
  medium: 24,
  large: 32,
  xlarge: 48,
};

// 获取尺寸值
const getSize = (size: MaterialSymbolSize): number => {
  if (typeof size === 'number') return size;
  return SIZE_MAP[size];
};

// Material Symbol 组件
export const MaterialSymbol: React.FC<MaterialSymbolProps> = ({
  name,
  size = 'medium',
  color = '#000000',
  style,
  weight = 400,
  symbolStyle = 'outlined',
  className,
}) => {
  const iconSize = getSize(size);
  const symbol = MATERIAL_SYMBOLS_MAP[name] || MATERIAL_SYMBOLS_MAP.help;

  const iconStyle: TextStyle = {
    fontFamily: `Material Symbols ${
      symbolStyle === 'outlined'
        ? 'Outlined'
        : symbolStyle === 'rounded'
        ? 'Rounded'
        : 'Sharp'
    }`,
    fontSize: iconSize,
    color,
    fontWeight: weight.toString() as any,
    lineHeight: iconSize,
    textAlign: 'center',
    ...style,
  };

  return (
    <View
      style={[
        {
          width: iconSize,
          height: iconSize,
          justifyContent: 'center',
          alignItems: 'center',
        },
      ]}
      className={className}
    >
      <Text style={iconStyle} selectable={false}>
        {symbol}
      </Text>
    </View>
  );
};

// 便捷图标组件 - 针对 M3 常用图标
export const M3Icons = {
  // 基础操作图标
  Add: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="add" {...props} />
  ),
  Edit: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="edit" {...props} />
  ),
  Delete: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="delete" {...props} />
  ),
  Close: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="close" {...props} />
  ),
  Check: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="check" {...props} />
  ),

  // 导航图标
  ArrowBack: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="arrow_back" {...props} />
  ),
  ArrowForward: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="arrow_forward" {...props} />
  ),
  Menu: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="menu" {...props} />
  ),

  // 用户界面图标
  Home: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="home" {...props} />
  ),
  Search: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="search" {...props} />
  ),
  Settings: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="settings" {...props} />
  ),

  // 社交图标
  Favorite: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="favorite" {...props} />
  ),
  Share: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="share" {...props} />
  ),
  Comment: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="comment" {...props} />
  ),

  // 内容图标
  AutoStories: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="auto_stories" {...props} />
  ),
  LibraryBooks: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="library_books" {...props} />
  ),

  // 状态图标
  Info: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="info" {...props} />
  ),
  Warning: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="warning" {...props} />
  ),
  Error: (props: Omit<MaterialSymbolProps, 'name'>) => (
    <MaterialSymbol name="error" {...props} />
  ),
};

// 导出默认图标组件
export default MaterialSymbol;
