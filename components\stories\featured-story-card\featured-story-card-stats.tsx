import React from 'react';
import { View } from 'react-native';

import { Text  } from '@/components/base';
import { Eye, Heart } from 'lucide-react-native';

interface FeaturedStoryCardStatsProps {
  authorName: string;
  views: number;
  likes: number;
}

export function FeaturedStoryCardStats({
  authorName,
  views,
  likes,
}: FeaturedStoryCardStatsProps) {
  return (
    <View className="flex flex-row justify-between items-center mb-3">
      <Text className="text-sm font-medium text-white">@{authorName}</Text>

      <View className="flex flex-row space-x-3">
        <View className="flex flex-row items-center space-x-1">
          <Eye size={12} color="#FFFFFF" />
          <Text className="text-xs text-white">{views}</Text>
        </View>

        <View className="flex flex-row items-center space-x-1">
          <Heart size={12} color="#FFFFFF" fill="#FFFFFF" />
          <Text className="text-xs text-white">{likes}</Text>
        </View>
      </View>
    </View>
  );
}
