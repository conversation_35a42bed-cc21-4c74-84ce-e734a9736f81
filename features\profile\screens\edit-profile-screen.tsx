import React, { useState, useEffect } from 'react';
import { Alert , View } from 'react-native';
import { useTranslation } from 'react-i18next';
import {
  getCurrentUserProfile,
  updateCurrentUserProfile,
  uploadAvatar,
  ProfileUpdateData,
} from '@/api/profiles';
import { useAuthStore } from '@/lib/store/auth-store';
import { useRouter } from 'expo-router';

// Gluestack UI components
import { ScrollView } from '@/components/ui/scroll-view';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { Center } from '@/components/ui/center/index';
import { Heading  } from '@/components/base';

// Components
import ProfileForm from '../components/profile-form';

export default function EditProfileScreen() {
  const { t } = useTranslation();
  const router = useRouter();

  const user = useAuthStore((state) => state.user);

  const [username, setUsername] = useState('');
  const [fullName, setFullName] = useState('');
  const [bio, setBio] = useState('');
  const [initialUsername, setInitialUsername] = useState('');
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [newAvatarUri, setNewAvatarUri] = useState<string | null>(null);

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) {
        Alert.alert(t('error'), t('authErrors.userNotFound'));
        setLoading(false);
        router.back();
        return;
      }
      setLoading(true);
      const { data, error } = await getCurrentUserProfile();
      if (error || !data) {
        Alert.alert(t('error'), t('profileErrors.fetchFailed'));
        console.error('Fetch profile error:', error);
      } else {
        setUsername(data.username || '');
        setInitialUsername(data.username || '');
        setFullName(data.full_name || '');
        setBio(data.bio || '');
        setAvatarUrl(data.avatar_url);
      }
      setLoading(false);
    };
    fetchProfile();
  }, [user, t, router]);

  /**
   * Handles the selection of a new avatar image
   * @param uri The local URI of the selected image
   */
  const handleAvatarSelected = async (uri: string) => {
    try {
      setNewAvatarUri(uri);
      setUploadingAvatar(true);

      const { data, error } = await uploadAvatar(uri);

      if (error) {
        console.error('Avatar upload error:', error);
        Alert.alert(
          t('error', 'Error'),
          t(
            'profileErrors.avatarUploadFailed',
            'Failed to upload avatar image.'
          )
        );
      } else if (data) {
        setAvatarUrl(data.avatar_url);
        Alert.alert(
          t('success', 'Success'),
          t('profileErrors.avatarUploadSuccess', 'Avatar updated successfully!')
        );
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      Alert.alert(
        t('error', 'Error'),
        t('profileErrors.avatarUploadFailed', 'Failed to upload avatar image.')
      );
    } finally {
      setUploadingAvatar(false);
      setNewAvatarUri(null);
    }
  };

  const handleSave = async () => {
    if (!user) {
      Alert.alert(t('error'), t('authErrors.userNotFound'));
      return;
    }
    if (!username.trim()) {
      Alert.alert(t('validationError'), t('profileErrors.usernameRequired'));
      return;
    }

    setSaving(true);
    const updates: ProfileUpdateData = {
      full_name: fullName,
      bio: bio,
    };
    // Only include username if it has changed, as it might have unique constraints
    if (username.trim() !== initialUsername) {
      updates.username = username.trim();
    }

    const { data, error } = await updateCurrentUserProfile(updates);
    setSaving(false);

    if (error) {
      console.error('Update profile error:', error);
      // Check for unique constraint violation for username (Postgres error code 23505)
      if (error.code === '23505' && error.message?.includes('username')) {
        Alert.alert(t('error'), t('profileErrors.usernameTaken'));
      } else {
        Alert.alert(t('error'), t('profileErrors.updateFailed'));
      }
    } else {
      Alert.alert(t('success'), t('profileErrors.updateSuccess'));
      // Optionally, update the authStore user's metadata if username changed,
      // or rely on a fresh fetch next time profile is viewed.
      router.back();
    }
  };

  if (loading) {
    return (
      <View className="flex justify-center items-center flex-1 bg-background-50 dark:bg-background-900">
        <M3EProgressIndicator size="large" color="$primary" />
      </View>
    );
  }

  return (
    <ScrollView
      className="flex-1 bg-background-50 dark:bg-background-900 px-4 py-4"
      keyboardShouldPersistTaps="handled"
    >
      <Heading className="text-2xl font-bold text-center mb-6 text-typography-900 dark:text-typography-100">
        {t('editProfileTitle', 'Edit Profile')}
      </Heading>

      <ProfileForm
        username={username}
        setUsername={setUsername}
        fullName={fullName}
        setFullName={setFullName}
        bio={bio}
        setBio={setBio}
        avatarUrl={avatarUrl}
        newAvatarUri={newAvatarUri}
        uploadingAvatar={uploadingAvatar}
        saving={saving}
        onAvatarSelected={handleAvatarSelected}
        onSave={handleSave}
      />
    </ScrollView>
  );
}
