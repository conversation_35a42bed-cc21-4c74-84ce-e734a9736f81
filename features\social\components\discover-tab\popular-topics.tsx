import React from 'react';
import { View } from 'react-native';

import { Text  } from '@/components/base';
import { useColorScheme } from 'nativewind';
import { TopicChip } from '../topic-chip';
import { useTranslation } from 'react-i18next';

interface PopularTopicsProps {
  onTopicPress: (topic: string) => void;
}

const MOCK_TOPICS = [
  '#AI创作',
  '#科幻世界',
  '#奇幻冒险',
  '#悬疑推理',
  '#都市传说',
  '#历史架空',
  '#青春文学',
  '#职场故事',
];

export function PopularTopics({ onTopicPress }: PopularTopicsProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { t } = useTranslation();

  return (
    <>
      <Text
        className={`text-xl font-bold mb-4 ${
          isDark ? 'text-typography-50' : 'text-typography-900'
        }`}
      >
        {t('social.discover.popularTopicsTitle')}
      </Text>

      <View className="flex-row flex-wrap gap-2 mb-12">
        {MOCK_TOPICS.map((topic) => (
          <TopicChip
            key={topic}
            topic={topic}
            onPress={() => onTopicPress(topic)}
          />
        ))}
      </View>
    </>
  );
}
