import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { BookOpen, Award, Heart, Star } from 'lucide-react-native';

import { Text  } from '@/components/base';



interface CreatorStats {
  totalStories: number;
  contributionStreak: number;
  totalLikes: number;
  avgCompletionRate: number;
}

interface CreatorStatsGridProps {
  stats: CreatorStats;
}

interface StatCardProps {
  icon: React.ReactNode;
  value: string | number;
  label: string;
}

// Internal component for each stat card
const StatCard = ({ icon, value, label }: StatCardProps) => {
  return (
    <View className="w-[48%] p-4 rounded-lg bg-background-100 dark:bg-background-800 items-center mb-4 border border-outline-200 dark:border-outline-700"
    >
      <View className="mb-2">{icon}</View>
      <Text className="font-bold text-xl text-typography-900 dark:text-typography-100 mt-1">
        {value}
      </Text>
      <Text className="text-xs text-typography-500 dark:text-typography-400 mt-1 text-center">
        {label}
      </Text>
    </View>
  );
};

export function CreatorStatsGrid({ stats }: CreatorStatsGridProps) {
  const { t } = useTranslation();

  return (
    <View className="mx-4 mb-6">
      <Text className="text-lg font-medium text-typography-900 dark:text-typography-100 mb-4">
        {t('creatorStats')}
      </Text>
      <View className="flex-row flex-wrap justify-between">
        <StatCard 
          icon={<BookOpen size={20} className="text-primary-500 dark:text-primary-400" />}
          value={stats.totalStories}
          label={t('totalStories')}
        />
        <StatCard 
          icon={<Award size={20} className="text-amber-500 dark:text-amber-400" />}
          value={stats.contributionStreak}
          label={t('contributionStreak')}
        />
        <StatCard 
          icon={<Heart size={20} className="text-red-500 dark:text-red-400" />}
          value={stats.totalLikes}
          label={t('totalLikes')}
        />
        <StatCard 
          icon={<Star size={20} className="text-indigo-500 dark:text-indigo-400" />}
          value={`${stats.avgCompletionRate}%`}
          label={t('avgCompletionRate')}
        />
      </View>
    </View>
  );
}
