import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Text  } from '@/components/base';

interface UserStatsProps {
  followers: number;
  following: number;
  storiesCount: number;
}

export default function UserStats({
  followers,
  following,
  storiesCount,
}: UserStatsProps) {
  const { t } = useTranslation();

  const StatItem = ({ value, label }: { value: number; label: string }) => (
    <View className="flex-1 items-center justify-center">
      <Text className="text-lg font-bold">{value}</Text>
      <Text className="text-sm text-typography-500 mt-1">{label}</Text>
    </View>
  );

  return (
    <View className="flex-row justify-around py-4 border-t border-b border-outline-200">
      <StatItem
        value={followers}
        label={t('social.userProfile.followers', '粉丝')}
      />

      <View className="w-[1px] h-10 my-auto bg-outline-200" />

      <StatItem
        value={following}
        label={t('social.userProfile.following', '关注中')}
      />

      <View className="w-[1px] h-10 my-auto bg-outline-200" />

      <StatItem
        value={storiesCount}
        label={t('social.userProfile.stories', '故事')}
      />
    </View>
  );
}
