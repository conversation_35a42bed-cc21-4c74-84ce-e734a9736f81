import React, { useState } from 'react';
import { View, Modal, Pressable } from 'react-native';
import { ChevronDown, Check } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

import { Text  } from '@/components/base';

export interface SortOption {
  id: string;
  label: string;
}

interface SortSelectorProps {
  options: SortOption[];
  selectedId: string;
  onSelect: (id: string) => void;
  label?: string;
}

export default function SortSelector({
  options,
  selectedId,
  onSelect,
  label,
}: SortSelectorProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const selectedOption = options.find((option) => option.id === selectedId);

  return (
    <View className="mx-4 my-2">
      {label && (
        <Text className="text-sm font-medium text-typography-500 dark:text-typography-400 mb-1">
          {label}
        </Text>
      )}
      <Pressable className={`flex-row items-center justify-between px-4 py-2 rounded-md ${
          isDark
            ? 'bg-background-800 border border-outline-700'
            : 'bg-background-100 border border-outline-200'
        }`}
        onPress={() => setModalVisible(true)}
      >
        <Text className="text-base text-typography-900 dark:text-typography-50">
          {selectedOption?.label || options[0]?.label}
        </Text>
        <ChevronDown
          size={16}
          className={isDark ? 'text-typography-50' : 'text-typography-900'}
        />
      </Pressable>

      <Modal visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View className="flex-1 justify-center items-center bg-black/50">
          <View
            className={`w-80 max-w-full mx-4 rounded-lg ${
              isDark ? 'bg-background-900' : 'bg-background-50'
            }`}
          >
            <View className="p-4">
              <Text className="text-lg font-semibold text-typography-900 dark:text-typography-50 mb-4">
                {label || t('common.selectOption', '选择选项')}
              </Text>
              <View className="flex flex-col w-full">
                {options.map((option) => (
                  <Pressable key={option.id}
                    className={`flex-row items-center justify-between py-4 border-b ${
                      isDark ? 'border-outline-700' : 'border-outline-200'
                    }`}
                    onPress={() => {
                      onSelect(option.id);
                      setModalVisible(false);
                    }}
                  >
                    <Text className="text-base text-typography-900 dark:text-typography-50">
                      {option.label}
                    </Text>
                    {option.id === selectedId && (
                      <Check size={16} className="text-primary-500" />
                    )}
                  </Pressable>
                ))}
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
