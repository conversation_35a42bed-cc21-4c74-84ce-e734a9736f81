import React, { useState, useCallback, useEffect } from 'react';
import { FlatList, RefreshControl , View } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import {
  RankingTabs,
  StoryRankingItem,
  AuthorRankingItem,
} from '@/features/rankings/components';
import { getTopStories, getTopAuthors, RankingPeriod } from '@/api/rankings';
import { Story } from '@/api/stories';
import { Profile } from '@/api/profiles';

// Import gluestack-ui components

import { Text  } from '@/components/base';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { SafeAreaView } from '@/components/ui/safe-area-view';

export default function RankingsScreen() {
  const { t } = useTranslation();
  const router = useRouter();

  const [activeTab, setActiveTab] = useState('stories');
  const [activePeriod, setActivePeriod] = useState<RankingPeriod>('week');
  const [stories, setStories] = useState<Story[]>([]);
  const [authors, setAuthors] = useState<Profile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load data based on active tab and period
  const loadData = useCallback(
    async (refresh = false) => {
      if (refresh) {
        setIsRefreshing(true);
      } else if (!isLoading) {
        setIsLoading(true);
      }

      setError(null);

      try {
        if (activeTab === 'stories') {
          const { data, error } = await getTopStories(activePeriod, {
            limit: 20,
          });

          if (error) throw error;

          if (data) {
            setStories(data);
          }
        } else if (activeTab === 'authors') {
          const { data, error } = await getTopAuthors(activePeriod, {
            limit: 20,
          });

          if (error) throw error;

          if (data) {
            setAuthors(data);
          }
        }
      } catch (err: any) {
        console.error('Failed to load rankings:', err);
        setError(err.message || t('rankings.loadError', '加载排行榜失败'));
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    },
    [activeTab, activePeriod, t, isLoading]
  );

  // Load data on mount and when tab or period changes
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  // Handle period change
  const handlePeriodChange = (period: RankingPeriod) => {
    setActivePeriod(period);
  };

  // Handle story press
  const handleStoryPress = (storyId: string) => {
    router.push(`/stories/${storyId}`);
  };

  // Handle author press
  const handleAuthorPress = (authorId: string) => {
    router.push(`/profile/${authorId}`);
  };

  // Render story item
  const renderStoryItem = ({ item, index }: { item: Story; index: number }) => (
    <StoryRankingItem
      story={item}
      rank={index + 1}
      onPress={handleStoryPress}
    />
  );

  // Render author item
  const renderAuthorItem = ({
    item,
    index,
  }: {
    item: Profile;
    index: number;
  }) => (
    <AuthorRankingItem
      author={item}
      rank={index + 1}
      onPress={handleAuthorPress}
    />
  );

  // Render empty state
  const renderEmpty = () => {
    if (isLoading && !isRefreshing) return null;

    return (
      <View className="p-6 items-center justify-center">
        <Text className="font-medium text-base text-secondary-600 dark:text-secondary-400 text-center">
          {activeTab === 'stories'
            ? t('rankings.noStories', '暂无故事')
            : t('rankings.noAuthors', '暂无作者')}
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-background-50 dark:bg-background-900">
      <Stack.Screen
        options={{
          title: t('rankings.title', '排行榜'),
          headerTitleStyle: {
            fontFamily: 'Inter-Medium',
            fontSize: 18,
          },
        }}
      />

      <RankingTabs
        activeTab={activeTab}
        onTabChange={handleTabChange}
        activePeriod={activePeriod}
        onPeriodChange={handlePeriodChange}
      />

      {isLoading && !isRefreshing ? (
        <View className="flex-1 justify-center items-center">
          <M3EProgressIndicator size="large" color="$primary500" />
        </View>
      ) : error ? (
        <View className="flex-1 justify-center items-center p-6">
          <Text className="font-medium text-base text-error-600 dark:text-error-400 text-center">
            {error}
          </Text>
        </View>
      ) : (
        <FlatList
          data={activeTab === 'stories' ? stories : authors}
          renderItem={
            activeTab === 'stories' ? renderStoryItem : renderAuthorItem
          }
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ padding: 16 }}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={() => loadData(true)}
              colors={['#6366f1']} // primary color
              tintColor="#6366f1" // primary color
            />
          }
          ListEmptyComponent={renderEmpty}
        />
      )}
    </SafeAreaView>
  );
}
