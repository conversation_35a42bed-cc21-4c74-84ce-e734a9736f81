import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { Text  } from '@/components/base';

export function EmptyState() {
  const { t } = useTranslation();

  return (
    <View className="flex-1 justify-center items-center p-4 bg-background-0">
      <Text className="text-base font-medium text-error-600 text-center">
        {t('storyDetail.errors.notFound', 'Story not found.')}
      </Text>
    </View>
  );
}
