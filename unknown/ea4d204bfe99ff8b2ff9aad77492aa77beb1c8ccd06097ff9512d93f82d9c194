import React from 'react';
import { Text } from '@/components/base';
import { Stack, useLocalSearchParams } from 'expo-router';
import StoryFeedScreen from '@/features/stories/screens/story-feed-screen'; // Using our new feed-style component
import { View } from 'react-native';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

export default function StoryDetailPageRoute() {
  const { id } = useLocalSearchParams<{ id?: string }>();
  const { mode } = useUnifiedTheme();

  if (!id) {
    // This should ideally not happen if navigation is correct
    return (
      <View className="flex-1 justify-center items-center bg-background-0 dark:bg-background-900">
        <Text className="text-typography-950 dark:text-typography-50">
          Story ID not found.
        </Text>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen options={{ title: 'Story', headerShown: false }} />
      {/* Using our new feed-style component with improved branch visualization */}
      <StoryFeedScreen storyId={id} />
    </>
  );
}
