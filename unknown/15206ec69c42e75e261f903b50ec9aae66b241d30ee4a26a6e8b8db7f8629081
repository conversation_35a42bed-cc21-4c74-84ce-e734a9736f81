/**
 * 检查 Gluestack UI 残余脚本
 * 
 * 此脚本用于检查项目中是否还有 Gluestack UI 的残余
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要检查的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts', '.backup-gluestack-removal'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts') || item.endsWith('.js'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 检查文件中的 Gluestack UI 残余
const checkFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 检查 @gluestack-ui 导入
    const gluestackImportRegex = /@gluestack-ui/g;
    const gluestackMatches = content.match(gluestackImportRegex);
    if (gluestackMatches) {
      issues.push(`发现 @gluestack-ui 导入 (${gluestackMatches.length} 处)`);
    }
    
    // 检查 tva 使用
    const tvaRegex = /\btva\s*\(/g;
    const tvaMatches = content.match(tvaRegex);
    if (tvaMatches) {
      issues.push(`发现 tva 函数使用 (${tvaMatches.length} 处)`);
    }
    
    // 检查 VariantProps 使用
    const variantPropsRegex = /VariantProps/g;
    const variantPropsMatches = content.match(variantPropsRegex);
    if (variantPropsMatches) {
      issues.push(`发现 VariantProps 使用 (${variantPropsMatches.length} 处)`);
    }
    
    // 检查 GluestackUIProvider 使用
    const gluestackProviderRegex = /GluestackUIProvider/g;
    const providerMatches = content.match(gluestackProviderRegex);
    if (providerMatches) {
      issues.push(`发现 GluestackUIProvider 使用 (${providerMatches.length} 处)`);
    }
    
    // 检查 withStyleContext 使用
    const withStyleContextRegex = /withStyleContext/g;
    const withStyleContextMatches = content.match(withStyleContextRegex);
    if (withStyleContextMatches) {
      issues.push(`发现 withStyleContext 使用 (${withStyleContextMatches.length} 处)`);
    }
    
    // 检查 createButton 等 Gluestack 工厂函数
    const createFunctionRegex = /create(Button|Input|Modal|Card|Text)/g;
    const createFunctionMatches = content.match(createFunctionRegex);
    if (createFunctionMatches) {
      issues.push(`发现 Gluestack 工厂函数 (${createFunctionMatches.length} 处): ${[...new Set(createFunctionMatches)].join(', ')}`);
    }
    
    return issues.length > 0 ? { file: filePath, issues } : null;
  } catch (error) {
    console.error(`读取文件 ${filePath} 时出错:`, error.message);
    return null;
  }
};

// 检查主题系统混乱
const checkThemeIssues = () => {
  const themeIssues = [];
  
  // 检查是否有多个主题提供者
  const themeProviders = [
    'lib/theme/unified-theme-provider.tsx',
    'components/ui/gluestack-ui-provider/index.tsx',
    'lib/theme/theme-provider.tsx'
  ];
  
  const existingProviders = themeProviders.filter(provider => {
    try {
      return fs.existsSync(provider);
    } catch {
      return false;
    }
  });
  
  if (existingProviders.length > 1) {
    themeIssues.push(`发现多个主题提供者: ${existingProviders.join(', ')}`);
  }
  
  // 检查重复的主题配置文件
  const themeConfigs = [
    'lib/theme/unified-theme-config.ts',
    'components/ui/gluestack-ui-provider/config.ts',
    'lib/theme/material3-theme.ts'
  ];
  
  const existingConfigs = themeConfigs.filter(config => {
    try {
      return fs.existsSync(config);
    } catch {
      return false;
    }
  });
  
  if (existingConfigs.length > 1) {
    themeIssues.push(`发现多个主题配置文件: ${existingConfigs.join(', ')}`);
  }
  
  return themeIssues;
};

// 检查重复的组件文件
const checkDuplicateComponents = () => {
  const duplicateIssues = [];
  
  // 检查 m3e-text 和 m3e-typography 重复
  const textComponents = [
    'components/ui/m3e-text',
    'components/ui/m3e-typography'
  ];
  
  const existingTextComponents = textComponents.filter(component => {
    try {
      return fs.existsSync(component);
    } catch {
      return false;
    }
  });
  
  if (existingTextComponents.length > 1) {
    duplicateIssues.push(`发现重复的文字组件: ${existingTextComponents.join(', ')}`);
  }
  
  return duplicateIssues;
};

// 主函数
const main = () => {
  console.log('🔍 开始检查 Gluestack UI 残余...');
  console.log('='.repeat(50));
  
  // 1. 检查代码文件中的残余
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  const filesWithIssues = [];
  
  for (const file of allFiles) {
    const result = checkFile(file);
    if (result) {
      filesWithIssues.push(result);
    }
  }
  
  // 2. 检查主题系统问题
  const themeIssues = checkThemeIssues();
  
  // 3. 检查重复组件
  const duplicateIssues = checkDuplicateComponents();
  
  // 4. 输出结果
  console.log('\n📊 检查结果:');
  console.log('='.repeat(50));
  
  if (filesWithIssues.length === 0 && themeIssues.length === 0 && duplicateIssues.length === 0) {
    console.log('✅ 没有发现 Gluestack UI 残余!');
    console.log('🎉 迁移完成，项目已完全清理!');
  } else {
    if (filesWithIssues.length > 0) {
      console.log(`⚠️  发现 ${filesWithIssues.length} 个文件存在 Gluestack UI 残余:`);
      filesWithIssues.forEach(({ file, issues }) => {
        console.log(`\n📄 ${file}:`);
        issues.forEach(issue => console.log(`   - ${issue}`));
      });
    }
    
    if (themeIssues.length > 0) {
      console.log('\n🎨 主题系统问题:');
      themeIssues.forEach(issue => console.log(`   - ${issue}`));
    }
    
    if (duplicateIssues.length > 0) {
      console.log('\n📦 重复组件问题:');
      duplicateIssues.forEach(issue => console.log(`   - ${issue}`));
    }
    
    console.log('\n📝 建议的修复步骤:');
    console.log('1. 手动清理上述文件中的 Gluestack UI 残余');
    console.log('2. 统一主题系统，只保留 UnifiedThemeProvider');
    console.log('3. 合并重复的组件文件');
    console.log('4. 运行应用程序测试功能');
  }
  
  console.log('\n' + '='.repeat(50));
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { 
  findAllFiles, 
  checkFile, 
  checkThemeIssues, 
  checkDuplicateComponents 
};
