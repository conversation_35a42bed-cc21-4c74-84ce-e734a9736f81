import React, { useState } from 'react';
import { Text } from '@/components/base';
import { View, Text, TouchableOpacity, Modal } from 'react-native';

// Time Picker 的属性接口
export interface M3ETimePickerProps {
  /** 当前时间值 */
  value?: Date;
  /** 时间变化事件 */
  onTimeChange?: (time: Date) => void;
  /** 是否使用 24 小时制 */
  is24Hour?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 标签文本 */
  label?: string;
  /** 占位符文本 */
  placeholder?: string;
  /** 自定义样式类名 */
  className?: string;
}

// 样式化的输入容器
/**
 * 格式化时间显示
 */
const formatTime = (date: Date, is24Hour: boolean = true): string => {
  const hours = date.getHours();
  const minutes = date.getMinutes();

  if (is24Hour) {
    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}`;
  } else {
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    const ampm = hours >= 12 ? 'PM' : 'AM';
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  }
};

/**
 * M3E Time Picker 组件
 *
 * 基于 Material Design 3 规范的时间选择器组件。
 *
 * @example
 * ```tsx
 * const [time, setTime] = useState(new Date());
 *
 * <M3ETimePicker
 *   value={time}
 *   onTimeChange={setTime}
 *   label="选择时间"
 *   is24Hour={true}
 * />
 * ```
 */
export const M3ETimePicker: React.FC<M3ETimePickerProps> = ({
  value,
  onTimeChange,
  is24Hour = true,
  disabled = false,
  label,
  placeholder = '选择时间',
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [selectedTime, setSelectedTime] = useState(value || new Date());

  const handlePress = () => {
    if (disabled) return;
    setIsVisible(true);
  };

  const handleConfirm = () => {
    onTimeChange?.(selectedTime);
    setIsVisible(false);
  };

  const handleCancel = () => {
    setSelectedTime(value || new Date());
    setIsVisible(false);
  };

  const adjustTime = (type: 'hour' | 'minute', delta: number) => {
    const newTime = new Date(selectedTime);
    if (type === 'hour') {
      newTime.setHours(newTime.getHours() + delta);
    } else {
      newTime.setMinutes(newTime.getMinutes() + delta);
    }
    setSelectedTime(newTime);
  };

  const displayValue = value ? formatTime(value, is24Hour) : placeholder;
  const containerClasses = `${className}`;

  return (
    <View className={containerClasses}>
      {/* 标签 */}
      {label && (
        <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
        </Text>
      )}

      {/* 输入框 */}
      <TouchableOpacity
        disabled={disabled}
        onPress={handlePress}
        className={`border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 bg-white dark:bg-gray-800 ${
          disabled ? 'opacity-50' : ''
        }`}
        activeOpacity={0.7}
      >
        <Text
          className={`text-base ${
            value
              ? 'text-gray-900 dark:text-white'
              : 'text-gray-500 dark:text-gray-400'
          }`}
        >
          {displayValue}
        </Text>
      </TouchableOpacity>

      {/* 时间选择器模态框 */}
      <Modal visible={isVisible}
        transparent
        animationType="fade"
        onRequestClose={handleCancel}
      >
        <View className="flex-1 bg-black/50 justify-center items-center p-4">
          <View className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-sm">
            {/* 标题 */}
            <Text className="text-xl font-medium text-gray-900 dark:text-white text-center mb-6">
              选择时间
            </Text>

            {/* 时间显示 */}
            <View className="flex-row justify-center items-center mb-8">
              <Text className="text-4xl font-light text-gray-900 dark:text-white">
                {formatTime(selectedTime, is24Hour)}
              </Text>
            </View>

            {/* 时间调整控件 */}
            <View className="flex-row justify-center items-center gap-8 mb-8">
              {/* 小时调整 */}
              <View className="items-center">
                <TouchableOpacity
                  onPress={() => adjustTime('hour', 1)}
                  className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full items-center justify-center mb-2"
                >
                  <Text className="text-xl text-purple-600 dark:text-purple-400">
                    +
                  </Text>
                </TouchableOpacity>
                <Text className="text-lg font-medium text-gray-900 dark:text-white">
                  {selectedTime.getHours().toString().padStart(2, '0')}
                </Text>
                <TouchableOpacity
                  onPress={() => adjustTime('hour', -1)}
                  className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full items-center justify-center mt-2"
                >
                  <Text className="text-xl text-purple-600 dark:text-purple-400">
                    -
                  </Text>
                </TouchableOpacity>
              </View>

              <Text className="text-2xl text-gray-900 dark:text-white">:</Text>

              {/* 分钟调整 */}
              <View className="items-center">
                <TouchableOpacity
                  onPress={() => adjustTime('minute', 1)}
                  className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full items-center justify-center mb-2"
                >
                  <Text className="text-xl text-purple-600 dark:text-purple-400">
                    +
                  </Text>
                </TouchableOpacity>
                <Text className="text-lg font-medium text-gray-900 dark:text-white">
                  {selectedTime.getMinutes().toString().padStart(2, '0')}
                </Text>
                <TouchableOpacity
                  onPress={() => adjustTime('minute', -1)}
                  className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full items-center justify-center mt-2"
                >
                  <Text className="text-xl text-purple-600 dark:text-purple-400">
                    -
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* 操作按钮 */}
            <View className="flex-row justify-end gap-4">
              <TouchableOpacity
                onPress={handleCancel}
                className="px-6 py-2 rounded-full"
              >
                <Text className="text-purple-600 dark:text-purple-400 font-medium">
                  取消
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleConfirm}
                className="px-6 py-2 bg-purple-600 rounded-full"
              >
                <Text className="text-white font-medium">确认</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default M3ETimePicker;
