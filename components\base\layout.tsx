/**
 * 基础布局组件
 * 替代 Gluestack UI 的 Box, VStack, HStack, Center 组件
 */

import React from 'react';
import { Box, VStack, HStack, Center } from '@/components/base';
import { View, ViewProps } from 'react-native';
import { cn, createVariants, spacing } from '@/utils/nativewind-helpers';

// Box 组件替代
export interface BoxProps extends ViewProps {
  className?: string;
}

export const Box = React.forwardRef<React.ComponentRef<typeof View>, BoxProps>(
  function Box({ className, ...props }, ref) {
    return (
      <View 
        ref={ref} 
        {...props} 
        className={cn('flex', className)} 
      />
    );
  }
);

Box.displayName = 'Box';

// VStack 组件替代
const vstackVariants = createVariants({
  base: 'flex flex-col',
  variants: {
    space: spacing,
    reversed: {
      true: 'flex-col-reverse',
    },
  },
});

export interface VStackProps extends ViewProps {
  className?: string;
  space?: keyof typeof spacing;
  reversed?: boolean;
}

export const VStack = React.forwardRef<React.ComponentRef<typeof View>, VStackProps>(
  function VStack({ className, space, reversed, ...props }, ref) {
    return (
      <View
        ref={ref}
        {...props}
        className={vstackVariants({ space, reversed, class: className })}
      />
    );
  }
);

VStack.displayName = 'VStack';

// HStack 组件替代
const hstackVariants = createVariants({
  base: 'flex flex-row',
  variants: {
    space: spacing,
    reversed: {
      true: 'flex-row-reverse',
    },
  },
});

export interface HStackProps extends ViewProps {
  className?: string;
  space?: keyof typeof spacing;
  reversed?: boolean;
}

export const HStack = React.forwardRef<React.ComponentRef<typeof View>, HStackProps>(
  function HStack({ className, space, reversed, ...props }, ref) {
    return (
      <View
        ref={ref}
        {...props}
        className={hstackVariants({ space, reversed, class: className })}
      />
    );
  }
);

HStack.displayName = 'HStack';

// Center 组件替代
export interface CenterProps extends ViewProps {
  className?: string;
}

export const Center = React.forwardRef<React.ComponentRef<typeof View>, CenterProps>(
  function Center({ className, ...props }, ref) {
    return (
      <View
        ref={ref}
        {...props}
        className={cn('flex items-center justify-center', className)}
      />
    );
  }
);

Center.displayName = 'Center';
