import React from 'react';
import { View } from 'react-native';
import { MoreVertical } from 'lucide-react-native';

// Import gluestack-ui components

import { Text  } from '@/components/base';
import { Pressable } from '@/components/base';


interface StoryListItemHeaderProps {
  title: string;
  onOptionsPress?: () => void;
}

export function StoryListItemHeader({
  title,
  onOptionsPress,
}: StoryListItemHeaderProps) {
  return (
    <View className="flex flex-row justify-between">
      <Text
        className="font-bold text-base text-typography-900 dark:text-typography-100"
        numberOfLines={1}
      >
        {title}
      </Text>

      {onOptionsPress && (
        <Pressable onPress={onOptionsPress}>
          <MoreVertical
            size={20}
            className="text-secondary-500 dark:text-secondary-400"
          />
        </Pressable>
      )}
    </View>
  );
}
